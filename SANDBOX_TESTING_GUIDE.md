# Snapback Sandbox Testing Guide

## 🎯 **Overview**

This guide helps you test Snapback's accessibility functionality in a sandboxed environment for App Store distribution.

## 🔧 **Prerequisites**

1. **Updated Entitlements**: Ensure you're using the updated entitlements files
2. **Clean Build**: Always clean and rebuild when testing sandbox changes
3. **Fresh Install**: Test with a completely fresh app installation

## 📋 **Testing Steps**

### **Step 1: Build Sandboxed Version**

```bash
# Clean build directory
rm -rf ~/Library/Developer/Xcode/DerivedData/Snapback-*

# Build with sandbox entitlements
xcodebuild -project Snapback.xcodeproj -scheme Snapback -configuration Debug build
```

### **Step 2: Install and Launch**

1. **Install the app** to Applications folder
2. **Launch Snapback** for the first time
3. **Check menu bar** - you should see the Snapback icon

### **Step 3: Permission Testing**

#### **Initial Permission Check**
1. Click the Snapback menu bar icon
2. You should see "Grant Accessibility Permissions" if not granted
3. Click it to see the enhanced permission dialog

#### **Permission Dialog Verification**
- Dialog should show **sandboxed-specific instructions**
- Should mention that sandboxed apps may require additional setup
- Should provide step-by-step guidance

#### **System Settings Verification**
1. Open System Settings → Privacy & Security → Accessibility
2. Look for "Snapback" in the list
3. **Note**: Sandboxed apps may not appear immediately

### **Step 4: Functionality Testing**

#### **Basic Accessibility Test**
1. Grant accessibility permissions
2. Try basic window snapping (Cmd+Ctrl+Left)
3. Check if windows actually move

#### **Advanced Testing**
1. Test workspace save/restore
2. Test drag-to-snap functionality
3. Test multi-monitor setups

### **Step 5: Diagnostic Tools**

#### **Use Built-in Diagnostics**
1. In DEBUG builds, go to menu: **Logging → Sandbox Diagnostic**
2. Review the diagnostic report
3. Copy report for troubleshooting

#### **Check Logs**
1. Use **Logging → Show Log Viewer**
2. Look for "SANDBOX DIAGNOSTIC" and "PERMISSION DIAGNOSTIC" entries
3. Filter by service: "SandboxUtility" or "PermissionManager"

## 🚨 **Common Issues & Solutions**

### **Issue 1: App Not Appearing in Accessibility Settings**

**Symptoms:**
- Snapback doesn't show up in System Settings → Accessibility
- Permission dialog appears but no toggle available

**Solutions:**
1. **Restart the app** completely
2. **Restart System Settings**
3. **Check entitlements** are properly applied
4. **Try launching from Terminal** to see error messages

### **Issue 2: Permissions Granted But Functionality Doesn't Work**

**Symptoms:**
- Accessibility toggle is ON in System Settings
- Window management commands don't work
- No error messages in logs

**Solutions:**
1. **Check diagnostic report** for functional test results
2. **Verify entitlements** include `com.apple.security.automation.apple-events`
3. **Test with non-sandboxed build** to isolate sandbox issues
4. **Check for additional macOS security prompts**

### **Issue 3: Intermittent Functionality**

**Symptoms:**
- Sometimes works, sometimes doesn't
- Functionality stops working after app restart
- Inconsistent behavior across different apps

**Solutions:**
1. **Check adaptive permission polling** in logs
2. **Verify persistent state** is being saved correctly
3. **Test with different target applications**
4. **Monitor system console** for security-related messages

## 🔍 **Debugging Commands**

### **Check Sandbox Status**
```bash
# Check if app is sandboxed
codesign -d --entitlements - /Applications/Snapback.app
```

### **Monitor System Logs**
```bash
# Watch for accessibility-related messages
log stream --predicate 'subsystem == "com.apple.accessibility"'

# Watch for sandbox-related messages
log stream --predicate 'subsystem == "com.apple.security.sandbox"'
```

### **Test Accessibility APIs**
```bash
# Check if accessibility is enabled system-wide
sqlite3 /Library/Application\ Support/com.apple.TCC/TCC.db \
  "SELECT client,auth_value FROM access WHERE service='kTCCServiceAccessibility';"
```

## 📊 **Expected Diagnostic Report**

A successful sandboxed setup should show:

```
=== Snapback Sandbox Diagnostic Report ===

Environment:
- Sandboxed: true
- macOS Version: 14.x.x
- App Version: x.x.x

Accessibility Status:
- Has Permission: true
- Can Create Elements: true
- Can Access Windows: true
- Can Modify Windows: true

Entitlements:
- com.apple.security.app-sandbox: true
- com.apple.security.automation.apple-events: true
- com.apple.security.temporary-exception.apple-events: [array]

Recommendations:
(Should be empty if everything is working)
```

## 🎯 **Success Criteria**

✅ **Sandbox Detection**: App correctly identifies as sandboxed  
✅ **Permission Dialog**: Shows sandboxed-specific instructions  
✅ **Accessibility Permission**: Appears in System Settings  
✅ **Basic Functionality**: Window snapping works  
✅ **Advanced Features**: Workspace save/restore works  
✅ **Diagnostic Report**: Shows all green status  

## 🆘 **Getting Help**

If you encounter issues:

1. **Generate diagnostic report** and include in bug reports
2. **Check logs** for specific error messages
3. **Test with non-sandboxed build** to isolate sandbox-specific issues
4. **Verify entitlements** are correctly applied to the built app

## 📝 **Notes for App Store Submission**

- Use `SnapbackRelease.entitlements` for App Store builds
- Test thoroughly on a clean macOS installation
- Document any temporary exceptions in App Store review notes
- Consider providing setup instructions for users
