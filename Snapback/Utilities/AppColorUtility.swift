import SwiftUI

/// Utility class for generating consistent app colors across the application
class AppColorUtility {
    // Enhanced color palette for app windows - more distinct colors
    static let appColors: [Color] = [
        // Primary colors
        .blue,
        .red,
        .green,

        // Secondary colors
        .orange,
        .purple,
        .pink,

        // Tertiary colors
        .teal,
        .indigo,
        .cyan,
    ]

    // Color cache to ensure consistency across UI updates
    private static var colorCache: [String: Color] = [:]
    private static let cacheQueue = DispatchQueue(
        label: "AppColorUtility.cache", attributes: .concurrent)

    /// Get a consistent color for an app based on its bundle ID and window info
    /// - Parameters:
    ///   - bundleID: The app's bundle identifier
    ///   - windowFrame: The window's frame (used to differentiate windows of the same app)
    ///   - opacity: Optional opacity to apply to the color (default: 1.0)
    /// - Returns: A consistent color for the app
    static func getAppColor(
        bundleID: String,
        windowFrame: CGRect,
        opacity: Double = 1.0
    ) -> Color {
        // Create a stable key for this window based on bundle ID and initial frame
        // Use rounded coordinates to create a stable identifier that doesn't change with minor position updates
        let stableX = round(windowFrame.origin.x * 10) / 10  // Round to 1 decimal place for stability
        let stableY = round(windowFrame.origin.y * 10) / 10
        let stableWidth = round(windowFrame.width * 10) / 10
        let stableHeight = round(windowFrame.height * 10) / 10

        let cacheKey = "\(bundleID)_\(stableX)_\(stableY)_\(stableWidth)_\(stableHeight)_\(opacity)"

        // Check cache first
        return cacheQueue.sync {
            if let cachedColor = colorCache[cacheKey] {
                return cachedColor
            }

            // Generate new color
            var hasher = Hasher()
            hasher.combine(bundleID)
            hasher.combine(stableX)
            hasher.combine(stableY)
            hasher.combine(stableWidth)
            hasher.combine(stableHeight)

            let hash = abs(hasher.finalize())
            let baseColor = appColors[hash % appColors.count]
            let finalColor = baseColor.opacity(opacity)

            // Cache the result
            cacheQueue.async(flags: .barrier) {
                colorCache[cacheKey] = finalColor
            }

            return finalColor
        }
    }

    /// Get a consistent color for an app based on its WindowInfo
    /// - Parameters:
    ///   - windowInfo: The WindowInfo object containing app and window details
    ///   - opacity: Optional opacity to apply to the color (default: 1.0)
    /// - Returns: A consistent color for the app, or gray if no bundle ID is available
    static func getAppColor(
        for windowInfo: WindowInfo,
        opacity: Double = 1.0
    ) -> Color {
        guard let bundleID = windowInfo.appBundleIdentifier else {
            return Color.gray.opacity(opacity)
        }

        return getAppColor(
            bundleID: bundleID,
            windowFrame: windowInfo.frame,
            opacity: opacity
        )
    }

    /// Clear the color cache (useful for testing or when workspace data changes significantly)
    static func clearCache() {
        cacheQueue.async(flags: .barrier) {
            colorCache.removeAll()
        }
    }

    /// Get cache statistics for debugging
    static func getCacheInfo() -> (count: Int, keys: [String]) {
        return cacheQueue.sync {
            return (count: colorCache.count, keys: Array(colorCache.keys))
        }
    }
}
