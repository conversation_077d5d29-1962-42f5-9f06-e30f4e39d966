import ApplicationServices
import Cocoa
import CoreGraphics
import Foundation
import IOKit

// MARK: - Display Information

struct DisplayInfo {
    let id: CGDirectDisplayID
    let physicalFrame: CGRect  // Physical pixel coordinates
    let logicalFrame: CGRect  // Logical point coordinates
    let visibleFrame: CGRect  // Usable area (excludes dock/menu bar)
    let scaleFactor: CGFloat  // Native scaling factor
    let isMain: Bool
    let name: String?

    // For backward compatibility, use logicalFrame as the main frame
    var frame: CGRect {
        return logicalFrame
    }

    // Legacy property for backward compatibility
    var heightDifferenceFromMain: CGFloat = 0.0

    // Computed properties for robust coordinate handling
    var effectiveOrigin: CGPoint {
        // Always use logical coordinates for consistency
        return logicalFrame.origin
    }

    var effectiveSize: CGSize {
        return logicalFrame.size
    }

    // Debug information
    var debugDescription: String {
        """
        Display \(id) \(isMain ? "(Main)" : ""):
        - Logical: \(logicalFrame)
        - Physical: \(physicalFrame)
        - Visible: \(visibleFrame)
        - Scale: \(scaleFactor)x
        - Name: \(name ?? "Unknown")
        """
    }

    // Create DisplayInfo from NSScreen
    static func from(_ screen: NSScreen) -> DisplayInfo {
        // Get the screen ID
        let screenID: CGDirectDisplayID
        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            screenID = CGDirectDisplayID(screenNumber.uint32Value)
        } else {
            // Use a fallback ID of 0
            screenID = 0
        }

        // Get both logical and physical bounds
        let logicalBounds = screen.frame
        let physicalBounds = screen.frame  // Use logical bounds as fallback for physical

        // Get display name safely
        let displayName = WindowLayoutManager.getDisplayName(for: screenID)

        // Calculate visible frame accounting for menu bar and dock
        let visibleFrame = WindowLayoutManager.calculateVisibleFrame(
            for: screenID, logicalBounds: logicalBounds)

        // Determine if this is the main display
        let isMainDisplay = screenID == CGMainDisplayID()

        // Create the DisplayInfo object
        let displayInfo = DisplayInfo(
            id: screenID,
            physicalFrame: physicalBounds,
            logicalFrame: logicalBounds,
            visibleFrame: visibleFrame,
            scaleFactor: screen.backingScaleFactor,
            isMain: isMainDisplay,
            name: displayName
        )

        // Log if this is the main display
        if displayInfo.isMain {
            LoggingService.shared.debug(
                "Display ID \(screenID) is the main display",
                service: "WindowLayoutManager",
                category: .workspaces
            )
        }

        return displayInfo
    }

}

// MARK: - Enhanced Coordinate System

/// Universal coordinate system that handles all display arrangements robustly
struct CoordinateSystem {
    let displays: [DisplayInfo]
    let virtualBounds: CGRect

    init(displays: [DisplayInfo]) {
        self.displays = displays
        // Calculate virtual bounds using logical frames
        let allFrames = displays.map { $0.logicalFrame }
        self.virtualBounds = allFrames.reduce(allFrames.first ?? .zero) { $0.union($1) }
    }

    /// Convert any frame to preview coordinates - works for any display arrangement
    func convertToPreview(
        _ frame: CGRect,
        previewSize: CGSize,
        padding: CGFloat = 10,
        isDisplay: Bool = false
    ) -> CGRect {
        // Calculate scale to fit virtual bounds in preview
        let availableSize = CGSize(
            width: previewSize.width - (padding * 2),
            height: previewSize.height - (padding * 2)
        )

        let scaleX = availableSize.width / virtualBounds.width
        let scaleY = availableSize.height / virtualBounds.height
        let scale = min(scaleX, scaleY) * 0.9  // Leave breathing room

        // Calculate scaled virtual size for centering
        let scaledVirtualSize = CGSize(
            width: virtualBounds.width * scale,
            height: virtualBounds.height * scale
        )

        // Center the scaled bounds in preview
        let centerOffsetX = (previewSize.width - scaledVirtualSize.width) / 2
        let centerOffsetY = (previewSize.height - scaledVirtualSize.height) / 2

        // Transform frame coordinates relative to virtual bounds
        let relativeX = frame.origin.x - virtualBounds.origin.x
        let relativeY = frame.origin.y - virtualBounds.origin.y

        // No Y-axis flipping needed - use coordinates as-is for proper display arrangement
        let finalY = relativeY

        // Apply scale and centering
        return CGRect(
            x: relativeX * scale + centerOffsetX,
            y: finalY * scale + centerOffsetY,
            width: max(frame.width * scale, 8),  // Minimum size
            height: max(frame.height * scale, 8)
        )
    }

    /// Get the display that contains the most of a given window frame
    func findBestDisplay(for windowFrame: CGRect) -> DisplayInfo? {
        var bestDisplay: DisplayInfo? = displays.first(where: { $0.isMain })
        var largestPercentage: CGFloat = 0.0

        for display in displays {
            // Check if display fully contains the window
            if display.logicalFrame.contains(windowFrame) {
                return display
            }

            // Calculate percentage of window in this display
            let intersection = display.logicalFrame.intersection(windowFrame)
            let percentage =
                (intersection.width * intersection.height)
                / (windowFrame.width * windowFrame.height)

            if percentage > largestPercentage {
                largestPercentage = percentage
                bestDisplay = display
            }
        }

        return bestDisplay
    }

    /// Find display by ID
    func findDisplay(by id: CGDirectDisplayID) -> DisplayInfo? {
        return displays.first { $0.id == id }
    }

    /// Determine display arrangement type
    func determineArrangement() -> String {
        guard displays.count > 1 else { return "Single Display" }

        let xDifference =
            displays.map { $0.logicalFrame.origin.x }.max()! - displays.map {
                $0.logicalFrame.origin.x
            }.min()!
        let yDifference =
            displays.map { $0.logicalFrame.origin.y }.max()! - displays.map {
                $0.logicalFrame.origin.y
            }.min()!

        if xDifference > yDifference {
            return "Horizontal"
        } else if yDifference > xDifference {
            return "Vertical"
        } else {
            return "Mixed"
        }
    }
}

// MARK: - App Window Snapshot

struct AppWindowSnapshot: Codable {
    let appName: String
    let bundleID: String
    let windowTitle: String
    let frameInDisplay: CGRect
    let displayID: CGDirectDisplayID
    let zIndex: Int
    let isFullscreen: Bool
    let isMinimized: Bool
    let spaceID: Int?
}

// MARK: - Display and Window Helpers

class WindowLayoutManager {
    private static let logger = LoggingService.shared
    private static let serviceName = "WindowLayoutManager"

    // Apps to exclude (e.g., Dock, Spotlight, Notification Center)
    static let excludedBundleIDs: Set<String> = [
        "com.apple.dock",
        "com.apple.Spotlight",
        "com.apple.notificationcenterui",
        "com.apple.WindowServer",
        "com.apple.ControlCenter",
    ]

    // MARK: Display Helpers

    static func getAllDisplays() -> [DisplayInfo] {
        var displays: [DisplayInfo] = []

        // Get list of all active displays using Core Graphics
        let maxDisplays: UInt32 = 32
        var displayIDs = [CGDirectDisplayID](repeating: 0, count: Int(maxDisplays))
        var displayCount: UInt32 = 0

        let result = CGGetActiveDisplayList(maxDisplays, &displayIDs, &displayCount)
        guard result == CGError.success else {
            logger.error("Failed to get display list: \(result)", service: serviceName)
            return []
        }

        let mainDisplayID = CGMainDisplayID()

        for i in 0..<Int(displayCount) {
            let displayID = displayIDs[i]

            // Get both logical and physical bounds
            let logicalBounds = CGDisplayBounds(displayID)
            let physicalBounds = logicalBounds  // Use logical bounds as fallback for physical

            // Get scale factor - use a simple approach
            let backingScaleFactor: CGFloat = 1.0  // Default scale factor

            // Get display name safely
            let displayName = WindowLayoutManager.getDisplayName(for: displayID)

            // Calculate visible frame accounting for menu bar and dock
            let visibleFrame = WindowLayoutManager.calculateVisibleFrame(
                for: displayID, logicalBounds: logicalBounds)

            let displayInfo = DisplayInfo(
                id: displayID,
                physicalFrame: physicalBounds,
                logicalFrame: logicalBounds,
                visibleFrame: visibleFrame,
                scaleFactor: backingScaleFactor,
                isMain: displayID == mainDisplayID,
                name: displayName
            )

            displays.append(displayInfo)
            logger.debug("Detected: \(displayInfo.debugDescription)", service: serviceName)
        }

        // Sort displays for consistent ordering (main display first, then by position)
        displays.sort { first, second in
            if first.isMain { return true }
            if second.isMain { return false }

            // Sort by position (left to right, top to bottom)
            if abs(first.effectiveOrigin.y - second.effectiveOrigin.y) < 10 {
                return first.effectiveOrigin.x < second.effectiveOrigin.x
            }
            return first.effectiveOrigin.y < second.effectiveOrigin.y
        }

        // Calculate height differences relative to the main display
        if let mainDisplay = displays.first(where: { $0.isMain }) {
            let mainHeight = mainDisplay.frame.height

            // Update each display with its height difference from the main display
            for i in 0..<displays.count {
                if !displays[i].isMain {
                    displays[i].heightDifferenceFromMain = mainHeight - displays[i].frame.height
                }
            }
        }

        // Create a comprehensive log of all displays
        var displayLog = """
            ┌─────────────────────────────────────────────────────────────────────────
            │ DISPLAY INFORMATION
            ├─────────────────────────────────────────────────────────────────────────
            │ - Total displays: \(displays.count)
            │ - Main display: \(displays.first(where: { $0.isMain })?.id ?? 0)
            │
            │ DISPLAYS:
            └─────────────────────────────────────────────────────────────────────────
            """

        for (index, display) in displays.enumerated() {
            let isRetina = display.scaleFactor > 1.0

            // Calculate edge differences
            let bottomDifference = display.visibleFrame.minY - display.frame.minY
            let topDifference = display.frame.maxY - display.visibleFrame.maxY
            let leftDifference = display.visibleFrame.minX - display.frame.minX
            let rightDifference = display.frame.maxX - display.visibleFrame.maxX

            // Calculate dock position
            let dockSides = [
                "bottom": topDifference,
                "left": leftDifference,
                "right": rightDifference,
            ]

            // dock position is the largest edge difference but if theres only one side with a difference, it's not the dock
            let dockPosition =
                dockSides.values.filter { $0 > 0 }.count == 1
                ? "none" : dockSides.max(by: { $0.value < $1.value })?.key ?? "none"

            // dock size is the largest edge difference but if there's no dock then the size is 0
            let dockSize = dockPosition == "none" ? 0 : dockSides[dockPosition] ?? 0

            // menubar height has to be the second largest edge difference
            let menuBarHeight =
                dockSides.filter { $0.key != dockPosition }.max(by: { $0.value < $1.value })?.value
                ?? 0

            displayLog += """

                ┌─────────────────────────────────────────────────────────────────────────
                │ DISPLAY #\(index): ID \(display.id) \(display.isMain ? "(MAIN DISPLAY)" : "")
                ├─────────────────────────────────────────────────────────────────────────
                │ MAIN STATUS:
                │ - IS MAIN: \(display.isMain)
                │ - COORDINATES: (\(display.frame.origin.x), \(display.frame.origin.y))
                │
                │ FRAME INFORMATION:
                │ - COORDS: x: \(display.frame.origin.x), y: \(display.frame.origin.y), width: \(display.frame.width), height: \(display.frame.height)
                │ - USABLE FRAME: x: \(display.visibleFrame.origin.x), y: \(display.visibleFrame.origin.y), width: \(display.visibleFrame.width), height: \(display.visibleFrame.height)
                │ - TOTAL FRAME: x: \(display.frame.origin.x), y: \(display.frame.origin.y), width: \(display.frame.width), height: \(display.frame.height)
                │
                │ POSITION DETAILS:
                │ - STARTING POINT  x: \(display.frame.origin.x), y: \(display.frame.origin.y)
                │ - NEEDS OFFSET: \(display.frame.origin.x != 0 || display.frame.origin.y != 0)
                │
                │ DISPLAY PROPERTIES:
                │ - SCALE FACTOR: \(display.scaleFactor)
                │ - IS RETINA: \(isRetina)
                │ - ORIENTATION: \(display.frame.width > display.frame.height ? "LANDSCAPE" : "PORTRAIT")
                │
                │ SYSTEM UI ELEMENTS:
                │
                │ - MENU BAR HEIGHT: \(menuBarHeight)
                │ - DOCK POSITION: \(dockPosition)
                │ - DOCK SIZE: \(dockSize)
                │ - EDGE DIFFERENCES: top: \(topDifference), bottom: \(bottomDifference), left: \(leftDifference), right: \(rightDifference)
                │
                │ HEIGHT DIFFERENCES:
                │ - HEIGHT DIFFERENCE FROM MAIN: \(display.heightDifferenceFromMain)
                │ - NORMALIZED HEIGHT DIFFERENCE: \(display.heightDifferenceFromMain / display.frame.height)
                └─────────────────────────────────────────────────────────────────────────
                """
        }

        // Log the comprehensive display information
        logger.debug(displayLog, service: serviceName, category: .workspaces)

        return displays
    }

    // MARK: - Helper Methods

    static func getDisplayName(for displayID: CGDirectDisplayID) -> String? {
        // Try to get the real monitor name using IOKit
        if let realName = getRealMonitorName(for: displayID) {
            return realName
        }

        // Fallback to generic names based on display type
        if displayID == CGMainDisplayID() {
            return "Built-in Display"
        } else {
            return "External Display"
        }
    }

    /// Get the real monitor name using NSScreen
    private static func getRealMonitorName(for displayID: CGDirectDisplayID) -> String? {
        // Try to find the NSScreen that corresponds to this display ID
        for screen in NSScreen.screens {
            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber,
                CGDirectDisplayID(screenNumber.uint32Value) == displayID
            {
                // Try to get the localized name from NSScreen (if available)
                if #available(macOS 10.15, *) {
                    let localizedName = screen.localizedName
                    if !localizedName.isEmpty {
                        logger.debug(
                            "Found localized display name '\(localizedName)' for display \(displayID)",
                            service: serviceName)
                        return localizedName
                    }
                }

                // For now, we'll return nil and let the fallback handle it
                // In the future, we could try other approaches like IOKit with proper availability checks
                break
            }
        }

        logger.debug("No display name found for display \(displayID)", service: serviceName)
        return nil
    }

    static func calculateVisibleFrame(
        for displayID: CGDirectDisplayID, logicalBounds: CGRect
    ) -> CGRect {
        // For main display, account for menu bar
        if displayID == CGMainDisplayID() {
            let menuBarHeight = NSStatusBar.system.thickness
            return CGRect(
                x: logicalBounds.origin.x,
                y: logicalBounds.origin.y,
                width: logicalBounds.width,
                height: logicalBounds.height - menuBarHeight
            )
        }

        // For other displays, use full bounds (no menu bar)
        return logicalBounds
    }

    // Structure to hold normalization result and log information
    struct NormalizationResult {
        let normalizedFrame: CGRect
        let displayID: CGDirectDisplayID
        let originalFrame: CGRect
        let displayFrame: CGRect
        let displayVisibleFrame: CGRect
        let scaleFactor: CGFloat
        let isMain: Bool
        let dx: CGFloat
        let dy: CGFloat
        let heightDifferenceFromMain: CGFloat
        let normalizedHeightDifference: CGFloat
        var appName: String?
        var bundleID: String?
        var windowTitle: String?
    }

    /// Calculates the normalized height difference for a display
    /// - Parameter display: The display to calculate the normalized height difference for
    /// - Returns: The normalized height difference (height difference divided by display height)
    static func calculateNormalizedHeightDifference(for display: DisplayInfo) -> CGFloat {
        return display.heightDifferenceFromMain / display.frame.height
    }

    /// Applies the height difference offset to a Y coordinate
    /// - Parameters:
    ///   - y: The original Y coordinate relative to the display
    ///   - windowHeight: The height of the window
    ///   - display: The display the window is on
    /// - Returns: The Y coordinate adjusted for height differences between displays
    static func applyHeightDifferenceOffset(
        y: CGFloat,
        windowHeight: CGFloat,
        display: DisplayInfo
    ) -> CGFloat {
        let normalizedHeightDifference = calculateNormalizedHeightDifference(for: display)
        // Use display.frame.height instead of windowHeight for the offset
        return y - (normalizedHeightDifference * display.frame.height)
    }

    /// Reverses the height difference offset applied during normalization
    /// - Parameters:
    ///   - normalizedY: The normalized Y coordinate
    ///   - windowHeight: The height of the window
    ///   - display: The display the window is on
    /// - Returns: The Y coordinate with the height difference offset removed
    static func reverseHeightDifferenceOffset(
        normalizedY: CGFloat,
        windowHeight: CGFloat,
        display: DisplayInfo
    ) -> CGFloat {
        let normalizedHeightDifference = calculateNormalizedHeightDifference(for: display)
        let y = (normalizedY * display.frame.height) + display.frame.minY
        // Use display.frame.height instead of windowHeight for the offset
        return y + (normalizedHeightDifference * display.frame.height)
    }

    static func normalizeFrame(
        _ windowFrame: CGRect, in display: DisplayInfo
    ) -> CGRect {
        // Calculate the position relative to the display's total frame (including menu bar)
        let dx = windowFrame.minX - display.frame.minX
        let adjustedY = windowFrame.minY - display.frame.minY

        // Apply height difference offset to Y coordinate before normalization
        let dy = applyHeightDifferenceOffset(
            y: adjustedY,
            windowHeight: windowFrame.height,
            display: display
        )

        // Calculate normalized coordinates (as a percentage of the display's total dimensions)
        let normalizedX = dx / display.frame.width
        let normalizedY = dy / display.frame.height
        let normalizedWidth = windowFrame.width / display.frame.width
        let normalizedHeight = windowFrame.height / display.frame.height

        // Create a normalized frame with values between 0 and 1
        let normalizedFrame = CGRect(
            x: normalizedX,
            y: normalizedY,
            width: normalizedWidth,
            height: normalizedHeight
        )

        return normalizedFrame
    }

    static func normalizeFrameWithLog(
        _ windowFrame: CGRect,
        in display: DisplayInfo,
        appName: String? = nil,
        bundleID: String? = nil,
        windowTitle: String? = nil
    ) -> NormalizationResult {
        // Calculate the position relative to the display's total frame (including menu bar)
        let dx = windowFrame.minX - display.frame.minX
        let adjustedY = windowFrame.minY - display.frame.minY

        // Apply height difference offset to Y coordinate before normalization
        let dy = applyHeightDifferenceOffset(
            y: adjustedY,
            windowHeight: windowFrame.height,
            display: display
        )

        // Calculate the normalized height difference for logging
        let normalizedHeightDifference = calculateNormalizedHeightDifference(for: display)

        // Calculate normalized coordinates (as a percentage of the display's total dimensions)
        let normalizedX = dx / display.frame.width
        let normalizedY = dy / display.frame.height
        let normalizedWidth = windowFrame.width / display.frame.width
        let normalizedHeight = windowFrame.height / display.frame.height

        // Create a normalized frame with values between 0 and 1
        let normalizedFrame = CGRect(
            x: normalizedX,
            y: normalizedY,
            width: normalizedWidth,
            height: normalizedHeight
        )

        // lets print out the formula and values so we can see what's going on

        print(
            """

            appName: \(appName ?? "unknown")
            display.frame: \(display.frame)
            windowFrame: \(windowFrame)
            windowFrame.minY: \(windowFrame.minY)
            windowFrame.minX: \(windowFrame.minX)
            display.frame.minY: \(display.frame.minY)
            display.frame.minX: \(display.frame.minX)
            dx: \(dx)
            adjustedY: \(adjustedY)
            normalizedHeightDifference: \(normalizedHeightDifference)
            dy: \(dy)

            -----------------

            FORMULA FOR DX: windowFrame.minX - display.frame.minX
            FORMULA FOR ADJUSTED Y: windowFrame.minY - display.frame.minY
            FORMULA FOR DY: adjustedY - (normalizedHeightDifference * windowFrame.height)
            -----------------

            RESULT DX: \(dx)
            RESULT ADJUSTED: Y \(adjustedY)
            RESULT DY: \(dy)
            """
        )

        // Return both the normalized frame and the information needed for logging
        return NormalizationResult(
            normalizedFrame: normalizedFrame,
            displayID: display.id,
            originalFrame: windowFrame,
            displayFrame: display.frame,
            displayVisibleFrame: display.visibleFrame,
            scaleFactor: display.scaleFactor,
            isMain: display.isMain,
            dx: dx,
            dy: dy,
            heightDifferenceFromMain: display.heightDifferenceFromMain,
            normalizedHeightDifference: normalizedHeightDifference,
            appName: appName,
            bundleID: bundleID,
            windowTitle: windowTitle
        )
    }

    static func denormalizeFrame(
        _ normalizedFrame: CGRect, in display: DisplayInfo
    ) -> CGRect {
        // Convert normalized coordinates (0-1) back to absolute coordinates
        let x = normalizedFrame.minX * display.frame.width + display.frame.minX
        let width = normalizedFrame.width * display.frame.width
        let height = normalizedFrame.height * display.frame.height

        // Reverse the height difference offset applied during normalization
        let y = reverseHeightDifferenceOffset(
            normalizedY: normalizedFrame.minY,
            windowHeight: height,
            display: display
        )

        // Create the global frame
        let globalFrame = CGRect(x: x, y: y, width: width, height: height)

        // Calculate the normalized height difference for logging
        let normalizedHeightDifference = calculateNormalizedHeightDifference(for: display)

        // Log the denormalization process for debugging
        let denormalizeLog = """
            ┌─────────────────────────────────────────────────────────────────────────
            │ DENORMALIZE FRAME
            ├─────────────────────────────────────────────────────────────────────────
            │ NORMALIZED FRAME:
            │ - X: \(normalizedFrame.minX), Y: \(normalizedFrame.minY), Width: \(normalizedFrame.width), Height: \(normalizedFrame.height)
            │
            │ DISPLAY INFO:
            │ - ID: \(display.id)
            │ - IS MAIN: \(display.isMain)
            │ - HEIGHT DIFFERENCE: \(display.heightDifferenceFromMain)
            │ - NORMALIZED HEIGHT DIFFERENCE: \(normalizedHeightDifference)
            │
            │ DENORMALIZED FRAME:
            │ - X: \(x), Y: \(y), Width: \(width), Height: \(height)
            └─────────────────────────────────────────────────────────────────────────
            """

        logger.debug(denormalizeLog, service: serviceName, category: .workspaces)

        return globalFrame
    }

    // Find the best display for a window frame
    static func findBestDisplay(for windowFrame: CGRect) -> DisplayInfo? {
        // Always get a fresh list of displays to ensure we have the latest arrangement
        let displays = getAllDisplays()

        // Log the window frame we're trying to place
        logger.debug(
            "Finding best display for window frame: \(windowFrame)",
            service: serviceName,
            category: .workspaces
        )

        var bestDisplay: DisplayInfo? = displays.first(where: { $0.isMain })
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        for display in displays {
            let currentFrameOfScreen = display.frame

            // First check if any screen fully contains the window
            if currentFrameOfScreen.contains(windowFrame) {
                bestDisplay = display
                break
            }

            // Calculate percentage of window in this screen
            let percentageOfRectWithinCurrentFrameOfScreen = percentageOf(
                windowFrame, withinFrameOfScreen: currentFrameOfScreen)

            if percentageOfRectWithinCurrentFrameOfScreen
                > largestPercentageOfRectWithinFrameOfScreen
            {
                largestPercentageOfRectWithinFrameOfScreen =
                    percentageOfRectWithinCurrentFrameOfScreen
                bestDisplay = display
            }
        }

        return bestDisplay
    }

    // Find DisplayInfo by display ID
    static func findDisplayInfo(for displayID: CGDirectDisplayID) -> DisplayInfo? {
        // Always get a fresh list of displays to ensure we have the latest arrangement
        let displays = getAllDisplays()

        // Log the display ID we're looking for
        logger.debug(
            "Finding display for ID: \(displayID)",
            service: serviceName,
            category: .workspaces
        )

        let display = displays.first { $0.id == displayID }

        if display != nil {
            logger.debug(
                "Found display with ID: \(displayID)",
                service: serviceName,
                category: .workspaces
            )
        } else {
            logger.debug(
                "No display found with ID: \(displayID)",
                service: serviceName,
                category: .workspaces
            )
        }

        return display
    }

    // Find DisplayInfo by UUID
    static func findDisplayInfoByUUID(_ uuid: UUID) -> DisplayInfo? {
        // Always get a fresh list of displays to ensure we have the latest arrangement
        let displays = getAllDisplays()

        // Log the UUID we're looking for
        logger.debug(
            "Finding display for UUID: \(uuid.uuidString)",
            service: serviceName,
            category: .workspaces
        )

        // Try to match by converting display IDs to UUIDs using the same method as WindowCaptureService
        for display in displays {
            let displayUUID = createConsistentUUID(from: display.id)
            if displayUUID == uuid {
                logger.debug(
                    "Found matching display with ID: \(display.id) for UUID: \(uuid.uuidString)",
                    service: serviceName,
                    category: .workspaces
                )
                return display
            }
        }

        // If no match found, return the main display as fallback
        logger.debug(
            "No matching display found for UUID: \(uuid.uuidString), using main display as fallback",
            service: serviceName,
            category: .workspaces
        )

        return displays.first { $0.isMain }
    }

    // Capture the current display arrangement as DisplayArrangementInfo
    static func captureCurrentDisplayArrangement() -> DisplayArrangementInfo {
        let displays = getAllDisplays()

        // Get all display IDs
        let displayIDs = displays.map { $0.id }

        // Find the main display ID
        let mainDisplayID = displays.first { $0.isMain }?.id ?? displayIDs.first ?? 0

        // Create a dictionary of display frames
        var displayFrames = [CGDirectDisplayID: CGRect]()
        for display in displays {
            displayFrames[display.id] = display.frame
        }

        // Create and return the DisplayArrangementInfo
        let arrangementInfo = DisplayArrangementInfo(
            displayIDs: displayIDs,
            mainDisplayID: mainDisplayID,
            displayFrames: displayFrames,
            createdAt: Date()
        )

        // Log the captured arrangement
        logger.info(
            """
            CAPTURED DISPLAY ARRANGEMENT:
            - Total displays: \(displayIDs.count)
            - Main display ID: \(mainDisplayID)
            - Display IDs: \(displayIDs)
            - Timestamp: \(arrangementInfo.createdAt)
            """,
            service: serviceName,
            category: .workspaces
        )

        return arrangementInfo
    }

    // Helper method to create a consistent UUID from a display ID (same as in WindowCaptureService)
    private static func createConsistentUUID(from displayID: CGDirectDisplayID) -> UUID {
        // Convert the display ID to a string
        let displayIDString = String(displayID)

        // Create a deterministic UUID by using a fixed pattern with the display ID
        let hexString = String(format: "%08x", displayID)
        let uuidString =
            "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"

        // Log the UUID creation
        logger.debug(
            "Creating consistent UUID for display ID: \(displayID) -> \(uuidString)",
            service: serviceName,
            category: .workspaces
        )

        // Create UUID from the formatted string, fallback to a random UUID if invalid
        let uuid = UUID(uuidString: uuidString) ?? UUID()

        // Log if we had to fall back to a random UUID
        if UUID(uuidString: uuidString) == nil {
            logger.warning(
                "Failed to create UUID from string: \(uuidString), using random UUID instead: \(uuid.uuidString)",
                service: serviceName,
                category: .workspaces
            )
        }

        return uuid
    }

    // MARK: Window Helpers

    static func getVisibleAppWindows() -> [AppWindowSnapshot] {
        guard
            let windowList = CGWindowListCopyWindowInfo(
                [.optionOnScreenOnly, .excludeDesktopElements], kCGNullWindowID)
                as? [[String: AnyObject]]
        else {
            return []
        }

        var snapshots: [AppWindowSnapshot] = []

        for (index, window) in windowList.enumerated() {
            guard
                let boundsDict = window[kCGWindowBounds as String] as? [String: Any],
                let appName = window[kCGWindowOwnerName as String] as? String,
                let bundleID = window[kCGWindowOwnerName as String] as? String,
                let windowTitle = window[kCGWindowName as String] as? String,
                let windowNumber = window[kCGWindowNumber as String] as? Int,
                !excludedBundleIDs.contains(bundleID)
            else { continue }

            // Convert bounds dictionary to CGRect
            var bounds = CGRect.zero
            if let cfDict = boundsDict as CFDictionary?,
                CGRectMakeWithDictionaryRepresentation(cfDict, &bounds)
            {

                // Find the display this window belongs to
                guard let display = findBestDisplay(for: bounds) else { continue }

                // Normalize the frame relative to the display and collect log information
                let normalizationResult = normalizeFrameWithLog(
                    bounds,
                    in: display,
                    appName: appName,
                    bundleID: bundleID,
                    windowTitle: windowTitle
                )
                let normalized = normalizationResult.normalizedFrame

                // Try to get space ID (may be nil if not available)
                let spaceID = getSpaceIDForWindow(windowNumber: windowNumber)

                // Check if window is minimized
                let isMinimized = window[kCGWindowIsOnscreen as String] as? Bool == false

                // Check if window is fullscreen
                let isFullscreen = (window[kCGWindowAlpha as String] as? Double ?? 1.0) == 0.0

                let snapshot = AppWindowSnapshot(
                    appName: appName,
                    bundleID: bundleID,
                    windowTitle: windowTitle,
                    frameInDisplay: normalized,
                    displayID: display.id,
                    zIndex: index,  // Use the index in the window list as z-index
                    isFullscreen: isFullscreen,
                    isMinimized: isMinimized,
                    spaceID: spaceID
                )

                snapshots.append(snapshot)
            }
        }

        // Sort snapshots by z-index
        let sortedSnapshots = snapshots.sorted { $0.zIndex < $1.zIndex }

        // Create a comprehensive log for window capture
        if !sortedSnapshots.isEmpty {
            var captureLog = """
                ┌─────────────────────────────────────────────────────────────────────────
                │ WINDOW CAPTURE STARTED
                ├─────────────────────────────────────────────────────────────────────────
                │ - Total windows captured: \(sortedSnapshots.count)
                │ - Excluded bundle IDs: \(excludedBundleIDs)
                │
                │ CAPTURED WINDOWS:
                └─────────────────────────────────────────────────────────────────────────
                """

            // Add details for each window
            for (index, snapshot) in sortedSnapshots.enumerated() {
                // Get the display info for this window
                let displayInfo =
                    findDisplayInfo(for: snapshot.displayID) ?? getAllDisplays().first!

                // Get the denormalized frame for reference
                let denormalizedFrame = denormalizeFrame(snapshot.frameInDisplay, in: displayInfo)

                captureLog += """

                    ┌─────────────────────────────────────────────────────────────────────────
                    │ WINDOW CAPTURE #\(index + 1) - \(snapshot.appName) - \(snapshot.windowTitle)
                    ├─────────────────────────────────────────────────────────────────────────
                    │ APP INFO:
                    │ - APP NAME: \(snapshot.appName)
                    │ - BUNDLE ID: \(snapshot.bundleID)
                    │ - WINDOW TITLE: \(snapshot.windowTitle)
                    │
                    │ DISPLAY INFO:
                    │ - DISPLAY ID: \(snapshot.displayID)
                    │ - FRAME: minX: \(displayInfo.frame.minX), minY: \(displayInfo.frame.minY), width: \(displayInfo.frame.width), height: \(displayInfo.frame.height)
                    │ - VISIBLE FRAME: minX: \(displayInfo.visibleFrame.minX), minY: \(displayInfo.visibleFrame.minY), width: \(displayInfo.visibleFrame.width), height: \(displayInfo.visibleFrame.height)
                    │ - SCALE FACTOR: \(displayInfo.scaleFactor)
                    │ - IS MAIN: \(displayInfo.isMain)
                    │
                    │ WINDOW FRAME:
                    │ - NORMALIZED: minX: \(snapshot.frameInDisplay.minX), minY: \(snapshot.frameInDisplay.minY), width: \(snapshot.frameInDisplay.width), height: \(snapshot.frameInDisplay.height)
                    │ - DENORMALIZED: minX: \(denormalizedFrame.minX), minY: \(denormalizedFrame.minY), width: \(denormalizedFrame.width), height: \(denormalizedFrame.height)
                    │
                    │ WINDOW STATE:
                    │ - Z-INDEX: \(snapshot.zIndex)
                    │ - FULLSCREEN: \(snapshot.isFullscreen)
                    │ - MINIMIZED: \(snapshot.isMinimized)
                    │ - SPACE ID: \(snapshot.spaceID ?? -1)
                    └─────────────────────────────────────────────────────────────────────────
                    """
            }

            // Log the comprehensive information
            logger.debug(captureLog, service: serviceName, category: .workspaces)
        }

        return sortedSnapshots
    }

    static func getSpaceIDForWindow(windowNumber: Int) -> Int? {
        guard let description = CGSCopyManagedDisplaySpaces() as? [[String: Any]] else {
            return nil
        }

        for screenDesc in description {
            if let spaces = screenDesc["Spaces"] as? [[String: Any]] {
                for space in spaces {
                    if let windows = space["Windows"] as? [Int], windows.contains(windowNumber) {
                        return space["id"] as? Int
                    }
                }
            }
        }
        return nil
    }

    static func restoreWindow(_ snapshot: AppWindowSnapshot, displays: [DisplayInfo]) {
        guard
            let app = NSRunningApplication.runningApplications(
                withBundleIdentifier: snapshot.bundleID
            ).first
        else {
            logger.warning(
                "App not running: \(snapshot.bundleID)",
                service: serviceName
            )
            return
        }

        let axApp = AXUIElementCreateApplication(app.processIdentifier)
        var value: AnyObject?
        AXUIElementCopyAttributeValue(axApp, kAXWindowsAttribute as CFString, &value)

        guard let windows = value as? [AXUIElement] else {
            logger.warning(
                "Could not get windows for app: \(snapshot.bundleID)",
                service: serviceName
            )
            return
        }

        for window in windows {
            var titleValue: CFTypeRef?
            if AXUIElementCopyAttributeValue(window, kAXTitleAttribute as CFString, &titleValue)
                == .success,
                let title = titleValue as? String,
                title == snapshot.windowTitle
            {

                var minimizedValue: CFTypeRef?
                if AXUIElementCopyAttributeValue(
                    window, kAXMinimizedAttribute as CFString, &minimizedValue) == .success,
                    let isMinimized = minimizedValue as? Bool,
                    isMinimized
                {
                    AXUIElementSetAttributeValue(
                        window, kAXMinimizedAttribute as CFString, kCFBooleanFalse)
                }

                if let display = displays.first(where: { $0.id == snapshot.displayID }) {
                    let globalFrame = denormalizeFrame(snapshot.frameInDisplay, in: display)
                    var position = globalFrame.origin
                    var size = globalFrame.size

                    if let positionValue = AXValueCreate(.cgPoint, &position) {
                        AXUIElementSetAttributeValue(
                            window, kAXPositionAttribute as CFString, positionValue)
                    }

                    if let sizeValue = AXValueCreate(.cgSize, &size) {
                        AXUIElementSetAttributeValue(
                            window, kAXSizeAttribute as CFString, sizeValue)
                    }

                    // Create a comprehensive log for window restoration
                    let restorationLog = """
                        ┌─────────────────────────────────────────────────────────────────────────
                        │ WINDOW RESTORATION - \(snapshot.appName)
                        ├─────────────────────────────────────────────────────────────────────────
                        │ APP INFO:
                        │ - NAME: \(snapshot.appName)
                        │ - BUNDLE ID: \(snapshot.bundleID)
                        │ - WINDOW TITLE: \(snapshot.windowTitle)
                        │
                        │ DISPLAY INFO:
                        │ - ID: \(display.id)
                        │ - FRAME: minX: \(display.frame.minX), minY: \(display.frame.minY), width: \(display.frame.width), height: \(display.frame.height), maxX: \(display.frame.maxX), maxY: \(display.frame.maxY)
                        │ - VISIBLE FRAME: minX: \(display.visibleFrame.minX), minY: \(display.visibleFrame.minY), width: \(display.visibleFrame.width), height: \(display.visibleFrame.height), maxX: \(display.visibleFrame.maxX), maxY: \(display.visibleFrame.maxY)
                        │ - SCALE FACTOR: \(display.scaleFactor)
                        │ - IS MAIN: \(display.isMain)
                        │
                        │ WINDOW FRAME:
                        │ - NORMALIZED: minX: \(snapshot.frameInDisplay.minX), minY: \(snapshot.frameInDisplay.minY), width: \(snapshot.frameInDisplay.width), height: \(snapshot.frameInDisplay.height), maxX: \(snapshot.frameInDisplay.maxX), maxY: \(snapshot.frameInDisplay.maxY)
                        │ - DENORMALIZED: minX: \(globalFrame.minX), minY: \(globalFrame.minY), width: \(globalFrame.width), height: \(globalFrame.height), maxX: \(globalFrame.maxX), maxY: \(globalFrame.maxY)
                        │
                        │ WINDOW STATE:
                        │ - FULLSCREEN: \(snapshot.isFullscreen)
                        │ - MINIMIZED: \(snapshot.isMinimized)
                        │ - SPACE ID: \(snapshot.spaceID ?? -1)
                        │ - Z-INDEX: \(snapshot.zIndex)
                        └─────────────────────────────────────────────────────────────────────────
                        """

                    logger.debug(restorationLog, service: serviceName, category: .workspaces)
                }

                // Attempt to bring to front
                AXUIElementSetAttributeValue(window, kAXMainAttribute as CFString, kCFBooleanTrue)
                AXUIElementSetAttributeValue(
                    window, kAXFrontmostAttribute as CFString, kCFBooleanTrue)

                break
            }
        }
    }

    // Helper for calculating the percentage of a window that is within a screen's frame
    static func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect)
        -> CGFloat
    {
        let intersection = rect.intersection(frameOfScreen)
        var result: CGFloat = 0.0

        if !intersection.isNull {
            let areaOfIntersection = intersection.width * intersection.height
            let areaOfRect = rect.width * rect.height
            result = areaOfRect > 0 ? areaOfIntersection / areaOfRect : 0
        }

        return result
    }

    static func convertToPreview(
        frame: CGRect, from display: DisplayInfo, targetPreviewSize: CGSize
    ) -> CGRect {
        let scaleX = targetPreviewSize.width / display.frame.width
        let scaleY = targetPreviewSize.height / display.frame.height
        let scale = min(scaleX, scaleY)

        return CGRect(
            x: frame.origin.x * scale,
            y: frame.origin.y * scale,
            width: frame.width * scale,
            height: frame.height * scale
        )
    }

    // Convert window frame to a preview frame based on the union of all screens.
    static func convertToPreview(
        _ windowRect: CGRect, allScreens: [NSScreen], previewSize: CGSize, padding: CGFloat
    ) -> CGRect {
        // Get all screen frames
        let allFrames = allScreens.map { $0.frame }

        // Create a union rect of all screens
        let unionRect = allFrames.reduce(allFrames.first ?? .zero) { $0.union($1) }

        // Calculate the scale factor to fit all screens in the preview
        let scaleX = (previewSize.width - padding * 2) / unionRect.width
        let scaleY = (previewSize.height - padding * 2) / unionRect.height
        let scale = min(scaleX, scaleY)

        // Calculate the scaled dimensions of the window
        let scaledWidth = windowRect.width * scale
        let scaledHeight = windowRect.height * scale

        // Calculate the position of the window in the preview
        let scaledX = (windowRect.minX - unionRect.minX) * scale + padding
        let scaledY = (windowRect.minY - unionRect.minY) * scale + padding

        return CGRect(x: scaledX, y: scaledY, width: scaledWidth, height: scaledHeight)
    }

    /// Convert a denormalized window frame to preview coordinates
    /// - Parameters:
    ///   - denormalizedFrame: The window frame in global coordinates
    ///   - unionRect: The union rect of all displays
    /// - Returns: The window frame in preview coordinates
    static func convertToPreview(_ denormalizedFrame: CGRect, unionRect: CGRect) -> CGRect {
        return CGRect(
            x: denormalizedFrame.minX - unionRect.minX,
            y: unionRect.maxY - denormalizedFrame.maxY,  // Flip Y-axis for SwiftUI coordinate system
            width: denormalizedFrame.width,
            height: denormalizedFrame.height
        )
    }

    /// Convert a denormalized window frame to preview coordinates with scaling and padding
    /// - Parameters:
    ///   - denormalizedFrame: The window frame in global coordinates
    ///   - unionRect: The union rect of all displays
    ///   - scale: Scale factor to apply to the coordinates
    ///   - padding: Padding to add to the coordinates
    /// - Returns: The window frame in preview coordinates
    static func convertToPreview(
        _ denormalizedFrame: CGRect, unionRect: CGRect, scale: CGFloat, padding: CGFloat,
        previewWidth: CGFloat = 500, previewHeight: CGFloat = 220
    ) -> CGRect {
        // Calculate the total available space in the preview
        let totalWidth = unionRect.width * scale
        let totalHeight = unionRect.height * scale

        // Calculate the center offset to position the content in the middle
        // Use exact centering calculation
        let centerOffsetX = (previewWidth - totalWidth) / 2
        let centerOffsetY = (previewHeight - totalHeight) / 2

        // Apply the center offset in addition to the regular padding
        // For perfect centering, we don't add padding to the offset calculation
        return CGRect(
            x: (denormalizedFrame.minX - unionRect.minX) * scale + centerOffsetX,
            y: (unionRect.maxY - denormalizedFrame.maxY) * scale + centerOffsetY,  // Flip Y-axis for SwiftUI coordinate system
            width: denormalizedFrame.width * scale,
            height: denormalizedFrame.height * scale
        )
    }

    // MARK: Serialization

    static func saveSnapshotsToDisk(_ snapshots: [AppWindowSnapshot], at url: URL) throws {
        let encoder = JSONEncoder()
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        let data = try encoder.encode(snapshots)
        try data.write(to: url)

        logger.info(
            "Saved \(snapshots.count) window snapshots to \(url.path)",
            service: serviceName
        )
    }

    static func loadSnapshotsFromDisk(from url: URL) throws -> [AppWindowSnapshot] {
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        let snapshots = try decoder.decode([AppWindowSnapshot].self, from: data)

        logger.info(
            "Loaded \(snapshots.count) window snapshots from \(url.path)",
            service: serviceName
        )

        return snapshots
    }
}

// MARK: - CGS Helper (Private API Usage)

@_silgen_name("CGSCopyManagedDisplaySpaces")
func CGSCopyManagedDisplaySpaces() -> CFArray?
