import Cocoa
import Foundation

/// Service for positioning windows on the main display with top alignment
class WindowPositioningService {
    static let shared = WindowPositioningService()

    private let logger = LoggingService.shared
    private let serviceName = "WindowPositioningService"

    private init() {}

    // MARK: - Public Interface

    /// Positions a window on the main display, snapped to the top to avoid dock
    /// - Parameters:
    ///   - window: The NSWindow to position
    ///   - preferredSize: Optional preferred size for the window
    ///   - context: Context about how the window was triggered (unused but kept for compatibility)
    func positionWindow(
        _ window: NSWindow, preferredSize: NSSize? = nil, context: WindowContext = .unknown
    ) {
        logger.debug("Positioning window on main display", service: serviceName)

        // Set preferred size if provided
        if let size = preferredSize {
            window.setContentSize(size)
        }

        // Always use main display
        let mainDisplay = getMainDisplay()

        // Position the window on the main display
        centerWindow(window, onDisplay: mainDisplay)

        logger.debug("Window positioned on main display \(mainDisplay.id)", service: serviceName)
    }

    // MARK: - Helper Methods

    /// Gets the main display
    private func getMainDisplay() -> DisplayInfo {
        let displays = WindowLayoutManager.getAllDisplays()
        return displays.first { $0.isMain } ?? displays.first!
    }

    /// Centers a window on the specified display, snapped to top to avoid dock
    private func centerWindow(_ window: NSWindow, onDisplay display: DisplayInfo) {
        let displayFrame = display.logicalFrame
        let windowSize = window.frame.size

        // Find the NSScreen that matches our DisplayInfo
        let matchingScreen = NSScreen.screens.first { screen in
            abs(screen.frame.origin.x - displayFrame.origin.x) < 1.0
                && abs(screen.frame.origin.y - displayFrame.origin.y) < 1.0
        }

        // Use visible frame to account for menu bar and dock, fallback to display frame
        let visibleFrame = matchingScreen?.visibleFrame ?? displayFrame

        // Add some padding from edges for better visual appearance
        let padding: CGFloat = 20
        let effectiveFrame = CGRect(
            x: visibleFrame.origin.x + padding,
            y: visibleFrame.origin.y + padding,
            width: max(0, visibleFrame.width - 2 * padding),
            height: max(0, visibleFrame.height - 2 * padding)
        )

        // Calculate horizontal center, but snap to top to avoid dock
        let centerX = effectiveFrame.origin.x + (effectiveFrame.width - windowSize.width) / 2
        let topY = effectiveFrame.origin.y + effectiveFrame.height - windowSize.height  // Snap to top

        // Ensure window stays within effective bounds
        let clampedX = max(
            effectiveFrame.origin.x,
            min(centerX, effectiveFrame.origin.x + effectiveFrame.width - windowSize.width))
        let clampedY = max(
            effectiveFrame.origin.y,
            min(topY, effectiveFrame.origin.y + effectiveFrame.height - windowSize.height))

        let finalPosition = NSPoint(x: clampedX, y: clampedY)

        logger.debug("Window positioned at \(finalPosition) on main display \(display.id)", service: serviceName)
        window.setFrameOrigin(finalPosition)
    }
}

// MARK: - Window Context

/// Context information about how a window was triggered
enum WindowContext {
    case menuBar  // Triggered from menu bar/status item
    case workspaceManager  // Triggered from workspace manager window
    case settingsRow  // Triggered from settings row
    case keyboard  // Triggered from keyboard shortcut
    case unknown  // Unknown trigger source
}
