import AppKit

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class ScreenDetectionService: ScreenDetectable {
    private let logger = LoggingService.shared
    private let serviceName = "ScreenDetectionService"

    // MARK: - Rectangle's Screen Detection Approach

    /// Detect screens using <PERSON><PERSON><PERSON><PERSON>'s approach - determines current screen and adjacent screens
    func detectScreens(using windowElement: AXUIElement?) -> UsableScreens? {
        let screens = NSScreen.screens
        guard let firstScreen = screens.first else { return nil }

        logger.info(
            "Detecting screens for window management - total screens: \(screens.count)",
            service: serviceName
        )

        if screens.count == 1 {
            logger.debug("Single screen setup detected", service: serviceName)
            return UsableScreens(screens: [firstScreen], currentScreen: firstScreen)
        }

        // Order screens using Rectangle's logic
        let screensOrdered = orderScreens(screens: screens)

        // Log screen arrangement for debugging
        logScreenArrangement(screensOrdered)

        // Determine source screen based on window position
        var windowFrame = CGRect.zero
        if let windowElement = windowElement {
            // Try to get window frame synchronously using AX API directly
            var positionRef: AnyObject?
            var sizeRef: AnyObject?

            let positionError = AXUIElementCopyAttributeValue(
                windowElement,
                kAXPositionAttribute as CFString,
                &positionRef
            )

            let sizeError = AXUIElementCopyAttributeValue(
                windowElement,
                kAXSizeAttribute as CFString,
                &sizeRef
            )

            if positionError == .success && sizeError == .success,
                let positionValue = positionRef, let sizeValue = sizeRef
            {
                var position = CGPoint.zero
                var size = CGSize.zero
                AXValueGetValue(positionValue as! AXValue, .cgPoint, &position)
                AXValueGetValue(sizeValue as! AXValue, .cgSize, &size)

                windowFrame = CGRect(origin: position, size: size)
                logger.debug("Got window frame: \(windowFrame)", service: serviceName)
            } else {
                logger.warning("Failed to get window frame, using zero rect", service: serviceName)
            }
        }

        guard let sourceScreen = screenContaining(windowFrame, screens: screensOrdered) else {
            logger.warning(
                "Could not determine source screen, using first screen", service: serviceName)
            return UsableScreens(screens: screensOrdered, currentScreen: firstScreen)
        }

        logger.info(
            "Detected current screen: \(screenIdentifier(sourceScreen)) for window frame: \(windowFrame)",
            service: serviceName
        )

        _ = adjacent(toFrameOfScreen: sourceScreen.frame, screens: screensOrdered)

        return UsableScreens(screens: screensOrdered, currentScreen: sourceScreen)
    }

    func getScreenContaining(_ rect: CGRect) -> NSScreen? {
        let screens = NSScreen.screens

        logger.debug(
            "Finding screen containing rect: \(rect)", service: serviceName,
            category: .screenDetection)
        logger.debug(
            "Available screens: \(screens.count)", service: serviceName, category: .screenDetection)

        // Log information about all screens
        for (index, screen) in screens.enumerated() {
            logger.debug(
                "Screen \(index) frame: \(screen.frame)", service: serviceName,
                category: .screenDetection)
            logger.debug(
                "Screen \(index) visible frame: \(screen.visibleFrame)", service: serviceName,
                category: .screenDetection)

            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber
            {
                logger.debug(
                    "Screen \(index) number: \(screenNumber)", service: serviceName,
                    category: .screenDetection)
            }

            if screen == NSScreen.main {
                logger.debug(
                    "Screen \(index) is main screen", service: serviceName,
                    category: .screenDetection)
            }
        }

        // Use Rectangle's approach for screen detection
        var result: NSScreen? = NSScreen.main
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        // Convert to flipped coordinates for screen detection
        let normalizedRect = rect.screenFlipped

        logger.debug(
            "Normalized rect for screen detection: \(normalizedRect)", service: serviceName,
            category: .screenDetection)

        // First check if any screen fully contains the rect
        for (index, screen) in screens.enumerated() {
            let currentFrameOfScreen = screen.frame

            if currentFrameOfScreen.contains(normalizedRect) {
                logger.debug(
                    "Screen \(index) fully contains the rect", service: serviceName,
                    category: .screenDetection)
                result = screen
                break
            }

            // If no screen fully contains the rect, find the screen with the largest percentage
            let percentageOfRectWithinCurrentFrameOfScreen = percentageOf(
                normalizedRect, withinFrameOfScreen: currentFrameOfScreen)

            logger.debug(
                "Screen \(index) contains \(percentageOfRectWithinCurrentFrameOfScreen * 100)% of the rect",
                service: serviceName,
                category: .screenDetection)

            if percentageOfRectWithinCurrentFrameOfScreen
                > largestPercentageOfRectWithinFrameOfScreen
            {
                largestPercentageOfRectWithinFrameOfScreen =
                    percentageOfRectWithinCurrentFrameOfScreen
                result = screen
                logger.debug(
                    "New best screen by percentage: \(index)", service: serviceName,
                    category: .screenDetection)
            }
        }

        if let screenNumber = result?.deviceDescription[
            NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
        {
            logger.debug(
                "Selected screen, number: \(screenNumber)", service: serviceName,
                category: .screenDetection)
        }

        return result
    }

    /// Get all available screens
    func getAllScreens() -> [NSScreen] {
        let screens = NSScreen.screens

        logger.debug(
            "Getting all screens: \(screens.count) available", service: serviceName,
            category: .screenDetection)

        // Log information about all screens
        for (index, screen) in screens.enumerated() {
            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber
            {
                logger.debug(
                    "Screen \(index) number: \(screenNumber)", service: serviceName,
                    category: .screenDetection)
            }

            if screen == NSScreen.main {
                logger.debug(
                    "Screen \(index) is main screen", service: serviceName,
                    category: .screenDetection)
            }
        }

        return screens
    }

    /// Calculate the percentage of a rectangle that is within a screen's frame
    /// This matches Rectangle's implementation
    private func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect) -> CGFloat
    {
        let intersectionOfRectAndFrameOfScreen = rect.intersection(frameOfScreen)
        if intersectionOfRectAndFrameOfScreen.isNull {
            return 0.0
        }

        let areaOfRect = rect.width * rect.height
        if areaOfRect == 0.0 {
            return 0.0
        }

        let areaOfIntersectionOfRectAndFrameOfScreen =
            intersectionOfRectAndFrameOfScreen.width * intersectionOfRectAndFrameOfScreen.height
        return areaOfIntersectionOfRectAndFrameOfScreen / areaOfRect
    }

    // MARK: - Rectangle's Screen Detection Methods

    /// Rectangle's screen containing logic
    func screenContaining(_ rect: CGRect, screens: [NSScreen]) -> NSScreen? {
        var result: NSScreen? = NSScreen.main
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        logger.debug("Searching for screen containing rect: \(rect)", service: serviceName)

        for (index, currentScreen) in screens.enumerated() {
            let currentFrameOfScreen = currentScreen.frame
            let normalizedRect: CGRect = rect.screenFlipped

            logger.debug(
                "Screen \(index): frame=\(currentFrameOfScreen), normalized_rect=\(normalizedRect)",
                service: serviceName
            )

            if currentFrameOfScreen.contains(normalizedRect) {
                logger.info(
                    "Screen \(index) (\(screenIdentifier(currentScreen))) fully contains the rect",
                    service: serviceName
                )
                result = currentScreen
                break
            }

            let percentageOfRectWithinCurrentFrameOfScreen: CGFloat = percentageOf(
                normalizedRect, withinFrameOfScreen: currentFrameOfScreen)

            logger.debug(
                "Screen \(index) contains \(percentageOfRectWithinCurrentFrameOfScreen * 100)% of the rect",
                service: serviceName
            )

            if percentageOfRectWithinCurrentFrameOfScreen
                > largestPercentageOfRectWithinFrameOfScreen
            {
                largestPercentageOfRectWithinFrameOfScreen =
                    percentageOfRectWithinCurrentFrameOfScreen
                result = currentScreen
                logger.debug(
                    "New best screen by percentage: \(index) (\(screenIdentifier(currentScreen)))",
                    service: serviceName
                )
            }
        }

        if let result = result {
            logger.info(
                "Selected screen: \(screenIdentifier(result))",
                service: serviceName
            )
        }

        return result
    }

    /// Rectangle's screen ordering logic - handles vertical arrangements correctly
    func orderScreens(screens: [NSScreen]) -> [NSScreen] {
        let sortedScreens = screens.sorted(by: { screen1, screen2 in
            // First, check if one screen is completely above the other
            if screen2.frame.maxY <= screen1.frame.minY {
                return true
            }
            if screen1.frame.maxY <= screen2.frame.minY {
                return false
            }

            // For screens that overlap vertically or share edges, sort by vertical center first
            let screen1CenterY = screen1.frame.midY
            let screen2CenterY = screen2.frame.midY

            // If vertical centers are significantly different, sort by center Y (top to bottom)
            if abs(screen1CenterY - screen2CenterY) > 50 {
                return screen1CenterY > screen2CenterY  // Higher Y values first (top to bottom in screen coordinates)
            }

            // If vertical centers are similar, sort by horizontal position (left to right)
            return screen1.frame.minX < screen2.frame.minX
        })

        logger.debug(
            "Ordered screens: \(sortedScreens.map { screenIdentifier($0) })", service: serviceName)
        return sortedScreens
    }

    /// Rectangle's adjacent screen detection
    func adjacent(toFrameOfScreen frameOfScreen: CGRect, screens: [NSScreen]) -> AdjacentScreens? {
        // Find the current screen
        guard
            let currentScreen = screens.first(where: { screen in
                let frame = screen.frame
                return frame.equalTo(frameOfScreen)
            })
        else {
            return nil
        }

        if screens.count == 2 {
            let otherScreen = screens.first(where: { screen in
                let frame = screen.frame
                return !frame.equalTo(frameOfScreen)
            })
            if let otherScreen = otherScreen {
                return AdjacentScreens(prev: otherScreen, current: currentScreen, next: otherScreen)
            }
        } else if screens.count > 2 {
            let currentScreenIndex = screens.firstIndex(where: { screen in
                let frame = screen.frame
                return frame.equalTo(frameOfScreen)
            })
            if let currentScreenIndex = currentScreenIndex {
                let nextIndex = currentScreenIndex == screens.count - 1 ? 0 : currentScreenIndex + 1
                let prevIndex = currentScreenIndex == 0 ? screens.count - 1 : currentScreenIndex - 1

                let prevScreen = screens[prevIndex]
                let nextScreen = screens[nextIndex]

                logger.debug(
                    "Adjacent screens - prev: \(screenIdentifier(prevScreen)), next: \(screenIdentifier(nextScreen))",
                    service: serviceName
                )

                return AdjacentScreens(prev: prevScreen, current: currentScreen, next: nextScreen)
            }
        }

        return nil
    }

    private func screenIdentifier(_ screen: NSScreen) -> String {
        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            return "Screen#\(screenNumber)"
        }
        return "Unknown"
    }

    private func logScreenArrangement(_ screens: [NSScreen]) {
        logger.info("Screen arrangement (ordered):", service: serviceName)
        for (index, screen) in screens.enumerated() {
            let identifier = screenIdentifier(screen)
            let isMain = screen == NSScreen.main ? " (MAIN)" : ""
            logger.info(
                "  \(index): \(identifier) - frame: \(screen.frame)\(isMain)",
                service: serviceName
            )
        }
    }
}
