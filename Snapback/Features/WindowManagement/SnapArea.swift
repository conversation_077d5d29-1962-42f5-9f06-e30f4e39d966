import AppKit
import Foundation

/// Represents a region on the screen where windows can be snapped
struct SnapArea: Equatable {
    let screen: NSScreen
    let directional: Directional
    let action: WindowDirection
    
    static func == (lhs: SnapArea, rhs: SnapArea) -> Bool {
        return lhs.screen == rhs.screen &&
               lhs.directional == rhs.directional &&
               lhs.action == rhs.action
    }
}

/// Represents different regions of the screen for snapping
enum Directional: Int, Codable, CaseIterable {
    case topLeft = 1
    case top = 2
    case topRight = 3
    case left = 4
    case right = 5
    case bottomLeft = 6
    case bottom = 7
    case bottomRight = 8
    case center = 9
    
    var description: String {
        switch self {
        case .topLeft: return "Top Left"
        case .top: return "Top"
        case .topRight: return "Top Right"
        case .left: return "Left"
        case .right: return "Right"
        case .bottomLeft: return "Bottom Left"
        case .bottom: return "Bottom"
        case .bottomRight: return "Bottom Right"
        case .center: return "Center"
        }
    }
}

/// Configuration for a snap area
struct SnapAreaConfig: Codable {
    let action: WindowDirection?
    
    init(action: WindowDirection?) {
        self.action = action
    }
}