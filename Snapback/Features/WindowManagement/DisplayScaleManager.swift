import AppKit
import Foundation

/// Manager for handling scale factor conversions between displays with different resolutions
class DisplayScaleManager {
    static let shared = DisplayScaleManager()
    private let logger = LoggingService.shared
    private let serviceName = "DisplayScaleManager"

    /// Convert a rect from one screen's coordinate system to another, accounting for different scale factors
    func convertRect(rect: CGRect, fromScreen: NSScreen, toScreen: NSScreen) -> CGRect {
        // If the screens are the same, no conversion needed
        if fromScreen == toScreen {
            return rect
        }

        // Get the scale factors
        let fromScale = fromScreen.backingScaleFactor
        let toScale = toScreen.backingScaleFactor

        // Check if we have a vertical arrangement
        let isVerticalArrangement = isVerticalScreenArrangement(screens: NSScreen.screens)

        // Log the conversion
        logger.debug(
            "Converting rect between screens with different scale factors:", service: serviceName)
        logger.debug("  From scale: \(fromScale)", service: serviceName)
        logger.debug("  To scale: \(toScale)", service: serviceName)
        logger.debug("  Original rect: \(rect)", service: serviceName)
        logger.debug("  Is vertical arrangement: \(isVerticalArrangement)", service: serviceName)
        logger.debug("  From screen frame: \(fromScreen.frame)", service: serviceName)
        logger.debug("  To screen frame: \(toScreen.frame)", service: serviceName)

        // For vertical arrangements, we need special handling
        if isVerticalArrangement {
            // Determine if the source screen is above or below the target screen
            let isSourceAboveTarget = fromScreen.frame.minY > toScreen.frame.maxY
            let isSourceBelowTarget = fromScreen.frame.maxY < toScreen.frame.minY

            logger.debug("  Is source above target: \(isSourceAboveTarget)", service: serviceName)
            logger.debug("  Is source below target: \(isSourceBelowTarget)", service: serviceName)

            // For vertical arrangements with different scale factors, we need to be extra careful
            if fromScale != toScale {
                // Calculate the scale ratio
                let scaleRatio = toScale / fromScale

                // Create a new rect with the correct dimensions for the target screen
                var targetRect = CGRect(
                    x: rect.origin.x,
                    y: rect.origin.y,
                    width: rect.width * scaleRatio,
                    height: rect.height * scaleRatio
                )

                // Adjust the position based on the vertical arrangement
                if isSourceAboveTarget {
                    // If source is above target, position at the top of the target screen
                    targetRect.origin.y = toScreen.frame.maxY - targetRect.height
                } else if isSourceBelowTarget {
                    // If source is below target, position at the bottom of the target screen
                    targetRect.origin.y = toScreen.frame.minY
                }

                // Ensure the rect is properly aligned with pixel boundaries
                let alignedRect = CGRect(
                    x: floor(targetRect.origin.x),
                    y: floor(targetRect.origin.y),
                    width: floor(targetRect.width),
                    height: floor(targetRect.height)
                )

                logger.debug(
                    "  Converted rect (vertical arrangement): \(alignedRect)", service: serviceName)
                return alignedRect
            }
        }

        // If the scale factors are the same, we just need to adjust for the screen position
        if fromScale == toScale {
            // Convert to global coordinates
            let globalRect = CGRect(
                x: rect.origin.x + fromScreen.frame.origin.x,
                y: rect.origin.y + fromScreen.frame.origin.y,
                width: rect.width,
                height: rect.height
            )

            // Convert to the target screen's coordinate system
            let targetRect = CGRect(
                x: globalRect.origin.x - toScreen.frame.origin.x,
                y: globalRect.origin.y - toScreen.frame.origin.y,
                width: globalRect.width,
                height: globalRect.height
            )

            logger.debug("  Converted rect (same scale): \(targetRect)", service: serviceName)
            return targetRect
        }

        // Calculate the scale ratio
        let scaleRatio = toScale / fromScale

        // First convert to global coordinates
        let globalRect = CGRect(
            x: rect.origin.x + fromScreen.frame.origin.x,
            y: rect.origin.y + fromScreen.frame.origin.y,
            width: rect.width,
            height: rect.height
        )

        // Then adjust for the scale difference
        let scaledRect = CGRect(
            x: globalRect.origin.x,
            y: globalRect.origin.y,
            width: globalRect.width * scaleRatio,
            height: globalRect.height * scaleRatio
        )

        // Finally convert to the target screen's coordinate system
        let targetRect = CGRect(
            x: scaledRect.origin.x - toScreen.frame.origin.x,
            y: scaledRect.origin.y - toScreen.frame.origin.y,
            width: scaledRect.width,
            height: scaledRect.height
        )

        // Ensure the rect is properly aligned with pixel boundaries
        let alignedRect = CGRect(
            x: floor(targetRect.origin.x),
            y: floor(targetRect.origin.y),
            width: floor(targetRect.width),
            height: floor(targetRect.height)
        )

        logger.debug("  Converted rect (different scale): \(alignedRect)", service: serviceName)
        return alignedRect
    }

    /// Helper to determine if screens are arranged vertically
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if any screen is positioned below another screen
        var hasVerticalStacking = false

        // For macOS, we need to be more careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Calculate the vertical overlap percentage
            let verticalOverlap =
                min(screen1.frame.maxY, screen2.frame.maxY)
                - max(screen1.frame.minY, screen2.frame.minY)
            let minHeight = min(screen1.frame.height, screen2.frame.height)
            let verticalOverlapPercentage = verticalOverlap / minHeight

            // Calculate the horizontal overlap percentage
            let horizontalOverlap =
                min(screen1.frame.maxX, screen2.frame.maxX)
                - max(screen1.frame.minX, screen2.frame.minX)
            let minWidth = min(screen1.frame.width, screen2.frame.width)
            let horizontalOverlapPercentage = horizontalOverlap / minWidth

            // If there's significant horizontal overlap and minimal vertical overlap,
            // it's likely a vertical arrangement
            hasVerticalStacking =
                horizontalOverlapPercentage > 0.5 && verticalOverlapPercentage < 0.2

            // Also check if one screen is completely above the other
            if !hasVerticalStacking {
                hasVerticalStacking =
                    (screen1.frame.minY >= screen2.frame.maxY)
                    || (screen2.frame.minY >= screen1.frame.maxY)
            }
        } else {
            // For more than 2 screens, use the original approach
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        let isVertical = (totalHeight > totalWidth * 1.2) || hasVerticalStacking

        logger.debug("isVerticalScreenArrangement result: \(isVertical)", service: serviceName)
        logger.debug(
            "  Total width: \(totalWidth), total height: \(totalHeight)", service: serviceName)
        logger.debug("  Has vertical stacking: \(hasVerticalStacking)", service: serviceName)

        return isVertical
    }

    /// Adjust a rect for a specific screen, ensuring it fits within the screen's total frame
    func adjustRectForScreen(rect: CGRect, screen: NSScreen) -> CGRect {
        let totalFrame = screen.frame

        // Check if this is a bottom-aligned window
        let isBottomAligned =
            rect.origin.y + rect.height >= totalFrame.origin.y + totalFrame.height - 10

        // Log the adjustment with bottom alignment information
        logger.debug("Adjusting rect for screen:", service: serviceName)
        logger.debug("  Screen total frame: \(totalFrame)", service: serviceName)
        logger.debug("  Screen visible frame: \(screen.visibleFrame)", service: serviceName)
        logger.debug("  Original rect: \(rect)", service: serviceName)
        logger.debug("  Is bottom aligned: \(isBottomAligned)", service: serviceName)

        // Ensure the rect fits within the total frame
        var adjustedRect = rect

        // Ensure width doesn't exceed total frame width
        if adjustedRect.width > totalFrame.width {
            adjustedRect.size.width = totalFrame.width
        }

        // Ensure height doesn't exceed total frame height
        if adjustedRect.height > totalFrame.height {
            adjustedRect.size.height = totalFrame.height
        }

        // Ensure right edge doesn't exceed total frame right edge
        if adjustedRect.maxX > totalFrame.maxX {
            adjustedRect.origin.x = totalFrame.maxX - adjustedRect.width
        }

        // Ensure bottom edge doesn't exceed total frame bottom edge
        if adjustedRect.maxY > totalFrame.maxY {
            adjustedRect.origin.y = totalFrame.maxY - adjustedRect.height
        }

        // Ensure left edge isn't less than total frame left edge
        if adjustedRect.minX < totalFrame.minX {
            adjustedRect.origin.x = totalFrame.minX
        }

        // Ensure top edge isn't less than total frame top edge
        if adjustedRect.minY < totalFrame.minY {
            adjustedRect.origin.y = totalFrame.minY
        }

        // Ensure the rect is properly aligned with pixel boundaries
        let alignedRect = CGRect(
            x: floor(adjustedRect.origin.x),
            y: floor(adjustedRect.origin.y),
            width: floor(adjustedRect.width),
            height: floor(adjustedRect.height)
        )

        logger.debug("  Adjusted rect: \(alignedRect)", service: serviceName)
        return alignedRect
    }

    /// Convert a point from one screen's coordinate system to another
    func convertPoint(point: CGPoint, fromScreen: NSScreen, toScreen: NSScreen) -> CGPoint {
        // If the screens are the same, no conversion needed
        if fromScreen == toScreen {
            return point
        }

        // Get the scale factors
        let fromScale = fromScreen.backingScaleFactor
        let toScale = toScreen.backingScaleFactor

        // Convert to global coordinates
        let globalPoint = CGPoint(
            x: point.x + fromScreen.frame.origin.x,
            y: point.y + fromScreen.frame.origin.y
        )

        // If the scale factors are different, adjust for the scale difference
        let scaledPoint: CGPoint
        if fromScale != toScale {
            let scaleRatio = toScale / fromScale
            scaledPoint = CGPoint(
                x: globalPoint.x * scaleRatio,
                y: globalPoint.y * scaleRatio
            )
        } else {
            scaledPoint = globalPoint
        }

        // Convert to the target screen's coordinate system
        return CGPoint(
            x: floor(scaledPoint.x - toScreen.frame.origin.x),
            y: floor(scaledPoint.y - toScreen.frame.origin.y)
        )
    }
}
