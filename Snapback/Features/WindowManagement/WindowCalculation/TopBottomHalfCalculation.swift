import AppKit
import Foundation

/// Calculation class for top and bottom half window positions with multi-display support
class TopBottomHalfCalculation: WindowCalculation {
    private let logger = LoggingService.shared
    private let serviceName = "TopBottomHalfCalculation"

    override func calculate(_ params: WindowCalculationParameters) -> WindowCalculationResult? {
        let usableScreens = params.usableScreens

        switch DefaultsManager.shared.subsequentExecutionMode {
        case .acrossMonitor:
            return calculateAcrossDisplays(params)

        case .acrossAndResize:
            if usableScreens.numScreens == 1 {
                return calculateResize(params)
            }
            return calculateAcrossDisplays(params)

        case .resize:
            return calculateResize(params)

        case .none, .cycleMonitor:
            let screen = usableScreens.currentScreen
            let oneHalfRect = calculateFirstRect(params.asRectParams())
            return WindowCalculationResult(
                rect: oneHalfRect.rect,
                screen: screen,
                resultingAction: params.action
            )
        }
    }

    /// Calculate window position when moving across displays
    private func calculateAcrossDisplays(_ params: WindowCalculationParameters)
        -> WindowCalculationResult?
    {
        let usableScreens = params.usableScreens
        let window = params.window
        let action = params.action

        // Get the next screen
        let nextScreen = usableScreens.nextScreen()

        // Calculate the rect on the next screen
        let adjustedFrame = usableScreens.adjustedVisibleFrame(for: nextScreen)
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: adjustedFrame,
            action: action,
            frameOfScreen: nextScreen.frame
        )

        let rectResult = calculateFirstRect(rectParams)

        logger.debug("Moving window to next screen with action: \\(action)", service: serviceName)

        return WindowCalculationResult(
            rect: rectResult.rect,
            screen: nextScreen,
            resultingAction: action
        )
    }

    /// Calculate window position when resizing on the current screen
    private func calculateResize(_ params: WindowCalculationParameters) -> WindowCalculationResult?
    {
        let usableScreens = params.usableScreens
        let window = params.window
        let action = params.action
        let screen = usableScreens.currentScreen

        // Calculate the adjusted frame
        let adjustedFrame = usableScreens.adjustedVisibleFrame(for: screen)

        // Determine the new action based on the current action
        let newAction: WindowDirection

        switch action {
        case .topHalf:
            // For top half, we could resize to top third or two-thirds
            // Since we don't have those specific actions, we'll use top quarter for now
            newAction = .topLeftQuarter
        case .bottomHalf:
            // For bottom half, we could resize to bottom third or two-thirds
            // Since we don't have those specific actions, we'll use bottom quarter for now
            newAction = .bottomLeftQuarter
        default:
            newAction = action
        }

        // Calculate the rect with the new action
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: adjustedFrame,
            action: newAction,
            frameOfScreen: screen.frame
        )

        let rectResult = calculateRect(rectParams)

        logger.debug("Resizing window with new action: \\(newAction)", service: serviceName)

        return WindowCalculationResult(
            rect: rectResult.rect,
            screen: screen,
            resultingAction: newAction
        )
    }

    /// Calculate the initial rectangle for the action
    private func calculateFirstRect(_ params: RectCalculationParameters) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        switch params.action {
        case .topHalf:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX,
                    y: visibleFrameOfScreen.minY + visibleFrameOfScreen.height / 2,
                    width: visibleFrameOfScreen.width,
                    height: visibleFrameOfScreen.height / 2
                ))

        case .bottomHalf:
            // For bottom half, use Rectangle's approach: don't manually adjust the Y-coordinate
            let height = floor(visibleFrameOfScreen.height / 2)

            // Create a rect with the visible frame and adjust only the height
            // This is Rectangle's approach for bottom-aligned windows
            var rect = visibleFrameOfScreen
            rect.size.height = height

            // Log the screen frame vs visible frame
            LoggingService.shared.debug(
                "Screen frame: \(params.frameOfScreen), visible frame: \(visibleFrameOfScreen)",
                service: "TopBottomHalfCalculation"
            )

            // Log the calculation details
            LoggingService.shared.debug(
                "Bottom half calculation using Rectangle's approach - visibleFrame: \(visibleFrameOfScreen), height: \(height)",
                service: "TopBottomHalfCalculation"
            )
            LoggingService.shared.debug(
                "Resulting rect: \(rect)",
                service: "TopBottomHalfCalculation"
            )

            return RectResult(rect)

        default:
            // For other actions, use the standard calculation
            return super.calculateRect(params)
        }
    }
}
