import AppKit
import Foundation

class AlmostMaximizeCalculation: WindowCalculation {
    private let almostMaximizeHeight: CGFloat
    private let almostMaximizeWidth: CGFloat

    override init() {
        let defaults = DefaultsManager.shared
        self.almostMaximizeHeight = defaults.almostMaximizeHeight
        self.almostMaximizeWidth = defaults.almostMaximizeWidth
        super.init()
    }

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen
        var calculatedWindowRect = visibleFrameOfScreen

        calculatedWindowRect.size.height = round(visibleFrameOfScreen.height * almostMaximizeHeight)
        calculatedWindowRect.size.width = round(visibleFrameOfScreen.width * almostMaximizeWidth)

        // Center the window
        calculatedWindowRect.origin.x = round((visibleFrameOfScreen.width - calculatedWindowRect.width) / 2.0) + visibleFrameOfScreen.minX
        calculatedWindowRect.origin.y = round((visibleFrameOfScreen.height - calculatedWindowRect.height) / 2.0) + visibleFrameOfScreen.minY

        return RectResult(calculatedWindowRect)
    }
}