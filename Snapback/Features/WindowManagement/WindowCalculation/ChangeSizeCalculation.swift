import AppKit
import Foundation

protocol ChangeWindowDimensionCalculation {
    func resizedWindowRectIsTooSmall(windowRect: CGRect, visibleFrameOfScreen: CGRect) -> Bool
}

extension ChangeWindowDimensionCalculation {
    func minimumWindowWidth() -> CGFloat {
        return DefaultsManager.shared.minimumWindowWidth
    }

    func minimumWindowHeight() -> CGFloat {
        return DefaultsManager.shared.minimumWindowHeight
    }

    func resizedWindowRectIsTooSmall(windowRect: CGRect, visibleFrameOfScreen: CGRect) -> Bool {
        let minimumWindowRectWidth = floor(visibleFrameOfScreen.width * minimumWindowWidth())
        let minimumWindowRectHeight = floor(visibleFrameOfScreen.height * minimumWindowHeight())
        return (windowRect.width <= minimumWindowRectWidth)
            || (windowRect.height <= minimumWindowRectHeight)
    }
}

class ChangeSizeCalculation: WindowCalculation, ChangeWindowDimensionCalculation {
    private let screenEdgeGapSize: CGFloat
    private let sizeOffsetAbs: CGFloat
    private let curtainChangeSize: Bool

    override init() {
        let defaults = DefaultsManager.shared
        self.screenEdgeGapSize = defaults.gapSize
        self.sizeOffsetAbs = defaults.sizeOffset
        self.curtainChangeSize = defaults.curtainChangeSize
        super.init()
    }

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        let window = params.window
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        let sizeOffset: CGFloat
        switch params.action {
        case .larger, .largerWidth:
            sizeOffset = sizeOffsetAbs
        case .smaller, .smallerWidth:
            sizeOffset = -sizeOffsetAbs
        default:
            sizeOffset = 0
        }

        var resizedWindowRect = window.frame

        // Width calculation
        if [.larger, .smaller, .largerWidth, .smallerWidth].contains(params.action) {
            resizedWindowRect.size.width = resizedWindowRect.width + sizeOffset
            resizedWindowRect.origin.x = resizedWindowRect.minX - floor(sizeOffset / 2.0)

            if curtainChangeSize {
                resizedWindowRect = againstLeftAndRightScreenEdges(
                    originalWindowRect: window.frame,
                    resizedWindowRect: resizedWindowRect,
                    visibleFrameOfScreen: visibleFrameOfScreen
                )
            }
        }

        // Height calculation
        if [.larger, .smaller].contains(params.action) {
            resizedWindowRect.size.height = resizedWindowRect.height + sizeOffset
            resizedWindowRect.origin.y = resizedWindowRect.minY - floor(sizeOffset / 2.0)
        }

        // Check if the resized window is too small
        if resizedWindowRectIsTooSmall(
            windowRect: resizedWindowRect, visibleFrameOfScreen: visibleFrameOfScreen)
        {
            return RectResult(window.frame)
        }

        return RectResult(resizedWindowRect)
    }

    private func againstScreenEdge(_ gap: CGFloat) -> Bool {
        return abs(gap) <= screenEdgeGapSize
    }

    private func againstLeftAndRightScreenEdges(
        originalWindowRect: CGRect,
        resizedWindowRect: CGRect,
        visibleFrameOfScreen: CGRect
    ) -> CGRect {
        var result = resizedWindowRect

        let originalLeftGap = originalWindowRect.minX - visibleFrameOfScreen.minX
        let originalRightGap = visibleFrameOfScreen.maxX - originalWindowRect.maxX

        if againstScreenEdge(originalLeftGap) {
            result.origin.x = visibleFrameOfScreen.minX
        } else if againstScreenEdge(originalRightGap) {
            result.origin.x = visibleFrameOfScreen.maxX - result.width
        }

        return result
    }
}
