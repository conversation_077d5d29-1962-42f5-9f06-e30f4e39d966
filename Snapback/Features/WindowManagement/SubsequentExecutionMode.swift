import Foundation

/// Defines how window actions behave when executed multiple times
enum SubsequentExecutionMode: Int, Codable {
    /// Stay on current display
    case none = 0
    
    /// Move to next display without resizing
    case acrossMonitor = 1
    
    /// Move to next display and resize if single display
    case acrossAndResize = 2
    
    /// Resize on current display
    case resize = 3
    
    /// Cycle through displays
    case cycleMonitor = 4
    
    /// Default mode
    static let defaultMode: SubsequentExecutionMode = .none
    
    /// Human-readable description
    var description: String {
        switch self {
        case .none:
            return "Stay on current display"
        case .acrossMonitor:
            return "Move to next display"
        case .acrossAndResize:
            return "Move to next display (resize on single display)"
        case .resize:
            return "Resize on current display"
        case .cycleMonitor:
            return "Cycle through displays"
        }
    }
}