import Foundation

/// Log levels supported by the logging system
enum LogLevel: String, CaseIterable {
    case debug
    case info
    case warning
    case error

    var prefix: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        }
    }
}

/// Predefined log categories for grouping related logs
enum LogCategory: String, CaseIterable {
    // Core categories
    case general
    case system

    // Window management categories
    case windowSnapping
    case windowPositioning
    case windowCalculation

    // Screen-related categories
    case screenDetection
    case screenDebug

    // Feature-specific categories
    case workspaces
    // workspacePreview category removed
    case dragToSnap
    case menuBar

    // Other categories
    case accessibility
    case userInterface
    case shortcuts
    case permissions
    case startup
    case toast

    /// Returns a user-friendly display name for the category
    var displayName: String {
        switch self {
        case .general: return "General"
        case .system: return "System"
        case .windowSnapping: return "Window Snapping"
        case .windowPositioning: return "Window Positioning"
        case .windowCalculation: return "Window Calculation"
        case .screenDetection: return "Screen Detection"
        case .screenDebug: return "Screen Debugging"
        case .workspaces: return "Workspaces"
        // workspacePreview case removed
        case .dragToSnap: return "Drag to Snap"
        case .menuBar: return "Menu Bar"
        case .accessibility: return "Accessibility"
        case .userInterface: return "User Interface"
        case .shortcuts: return "Keyboard Shortcuts"
        case .permissions: return "Permissions"
        case .startup: return "Startup"
        case .toast: return "Toast Notifications"
        }
    }

    /// Groups categories by feature for the logging menu
    static var featureGroups: [String: [LogCategory]] {
        return [
            "Window Management": [.windowSnapping, .windowPositioning, .windowCalculation],
            "Screen Detection": [.screenDetection, .screenDebug],
            "Workspaces": [.workspaces],
            "User Interface": [.userInterface, .menuBar, .toast],
            "System": [.system, .startup, .permissions, .accessibility],
            "Shortcuts": [.shortcuts, .dragToSnap],
            "Other": [.general],
        ]
    }

    /// Maps service names to categories for automatic categorization
    static func categoryForService(_ service: String) -> LogCategory {
        switch service {
        // Screen-related services
        case "ScreenDetectionService":
            return .screenDetection
        case "ScreenDebugger", "ScreenshotDebugger":
            return .screenDebug

        // Window positioning services
        case "WindowMover", "WindowPositioner":
            return .windowPositioning
        case "WindowSnappingService", "SnappingManager":
            return .windowSnapping
        case "WindowCalculationService", "StandardPositionCalculation",
            "LeftRightHalfCalculation", "TopBottomHalfCalculation",
            "NonMainDisplayCalculation", "UsableScreens":
            return .windowCalculation

        // Workspace services (WorkspacePreviewView removed)

        // Workspace-related services
        case "WorkspaceService", "SaveWorkspaceView", "EditWorkspaceView",
            "WorkspaceManager", "DisplayPreviewView":
            return .workspaces

        // Drag-to-snap related services
        case "DragToSnapManager", "SnapAreaModel", "SnapAreaView":
            return .dragToSnap

        // Menu bar related services
        case "StatusBarManager", "MenuManager":
            return .menuBar

        // Accessibility related services
        case "AccessibilityElement", "AccessibilityManager":
            return .accessibility

        // Shortcut related services
        case "ShortcutService", "KeyboardShortcutManager":
            return .shortcuts

        // Permission related services
        case "PermissionManager":
            return .permissions

        // Startup related services
        case "StartupManager", "LaunchAtLogin":
            return .startup

        // Toast notification related services
        case "ToastManager", "ToastView", "ToastService":
            return .toast

        // UI related services
        case "AppDelegate", "SettingsView", "MainView":
            return .userInterface

        // System related services
        case "LoginItemManager", "DefaultsManager", "SystemManager":
            return .system

        // Default category for unspecified services
        default:
            return .general
        }
    }
}

/// Main logging service for the application
class LoggingService {
    static let shared = LoggingService()

    // MARK: - UserDefaults Keys

    struct Keys {
        static let enabledCategories = "LoggingService.enabledCategories"
        static let minimumLogLevel = "LoggingService.minimumLogLevel"
        static let loggingEnabled = "LoggingService.loggingEnabled"
    }

    // MARK: - Properties

    /// Whether logging is enabled globally
    var isLoggingEnabled: Bool {
        get { UserDefaults.standard.bool(forKey: Keys.loggingEnabled) }
        set { UserDefaults.standard.set(newValue, forKey: Keys.loggingEnabled) }
    }

    /// The minimum log level to display
    var minimumLogLevel: LogLevel {
        get {
            if let storedValue = UserDefaults.standard.string(forKey: Keys.minimumLogLevel),
                let level = LogLevel(rawValue: storedValue)
            {
                return level
            }
            return .debug  // Default to debug level
        }
        set {
            UserDefaults.standard.set(newValue.rawValue, forKey: Keys.minimumLogLevel)
        }
    }

    /// Categories that are currently enabled for logging
    private var enabledCategories: Set<LogCategory> {
        get {
            if let storedValues = UserDefaults.standard.stringArray(forKey: Keys.enabledCategories)
            {
                return Set(storedValues.compactMap { LogCategory(rawValue: $0) })
            }
            // By default, enable all categories
            return Set(LogCategory.allCases)
        }
        set {
            let rawValues = newValue.map { $0.rawValue }
            UserDefaults.standard.set(rawValues, forKey: Keys.enabledCategories)
        }
    }

    // MARK: - Initialization

    private init() {
        // Set default values if not already set
        if UserDefaults.standard.object(forKey: Keys.loggingEnabled) == nil {
            UserDefaults.standard.set(true, forKey: Keys.loggingEnabled)
        }

        if UserDefaults.standard.object(forKey: Keys.minimumLogLevel) == nil {
            UserDefaults.standard.set(LogLevel.debug.rawValue, forKey: Keys.minimumLogLevel)
        }

        if UserDefaults.standard.object(forKey: Keys.enabledCategories) == nil {
            let allCategories = LogCategory.allCases.map { $0.rawValue }
            UserDefaults.standard.set(allCategories, forKey: Keys.enabledCategories)
        }
    }

    // MARK: - Category Management

    /// Enable logging for a specific category
    func enableCategory(_ category: LogCategory) {
        var categories = enabledCategories
        categories.insert(category)
        enabledCategories = categories
    }

    /// Disable logging for a specific category
    func disableCategory(_ category: LogCategory) {
        var categories = enabledCategories
        categories.remove(category)
        enabledCategories = categories
    }

    /// Check if a category is enabled
    func isCategoryEnabled(_ category: LogCategory) -> Bool {
        return enabledCategories.contains(category)
    }

    /// Enable multiple categories at once
    func enableCategories(_ categories: [LogCategory]) {
        var currentCategories = enabledCategories
        categories.forEach { currentCategories.insert($0) }
        enabledCategories = currentCategories
    }

    /// Disable multiple categories at once
    func disableCategories(_ categories: [LogCategory]) {
        var currentCategories = enabledCategories
        categories.forEach { currentCategories.remove($0) }
        enabledCategories = currentCategories
    }

    /// Enable all categories
    func enableAllCategories() {
        enabledCategories = Set(LogCategory.allCases)
    }

    /// Disable all categories
    func disableAllCategories() {
        enabledCategories = []
    }

    // MARK: - Logging Methods

    /// Main logging method with category and level filtering
    func log(
        _ message: String, level: LogLevel = .info, service: String, category: LogCategory? = nil
    ) {
        // Skip logging if globally disabled
        guard isLoggingEnabled else { return }

        // Skip if log level is below minimum
        let logLevels: [LogLevel] = [.debug, .info, .warning, .error]
        guard let levelIndex = logLevels.firstIndex(of: level),
            let minLevelIndex = logLevels.firstIndex(of: minimumLogLevel),
            levelIndex >= minLevelIndex
        else {
            return
        }

        // Determine category from service name if not provided
        let logCategory = category ?? LogCategory.categoryForService(service)

        // Skip if category is disabled
        guard enabledCategories.contains(logCategory) else { return }

        // Format the log message
        let timestamp = DateFormatter.localizedString(
            from: Date(), dateStyle: .none, timeStyle: .medium)

        // Always print to Xcode console
        // Special formatting for workspace preview logs removed
        print(
            "\(level.prefix) [\(timestamp)] [\(service)] [\(logCategory.rawValue)] \(message)")
    }

    /// Log a debug message
    func debug(_ message: String, service: String, category: LogCategory? = nil) {
        log(message, level: .debug, service: service, category: category)
    }

    /// Log an info message
    func info(_ message: String, service: String, category: LogCategory? = nil) {
        log(message, level: .info, service: service, category: category)
    }

    /// Log a warning message
    func warning(_ message: String, service: String, category: LogCategory? = nil) {
        log(message, level: .warning, service: service, category: category)
    }

    /// Log an error message
    func error(_ message: String, service: String, category: LogCategory? = nil) {
        log(message, level: .error, service: service, category: category)
    }
}
