import AppKit
import SwiftUI

/// Window controller for the log viewer
class LogViewerWindowController: NSWindowController {

    // Singleton instance
    static let shared = LogViewerWindowController()

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "LogViewerWindowController"

    // Initialize with a window
    private init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 900, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        window.title = "Snapback Log Viewer"
        window.center()
        window.setFrameAutosaveName("LogViewerWindow")

        // Create the SwiftUI view that provides the window contents
        let logViewerView = LogViewerView()

        // Create the hosting controller
        let hostingController = NSHostingController(rootView: logViewerView)
        window.contentViewController = hostingController

        super.init(window: window)

        // Set the window delegate
        window.delegate = self
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    /// Show the log viewer window
    func showWindow() {
        if window?.isVisible == false {
            window?.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            logger.info("Log viewer window opened", service: serviceName)
        }
    }
}

// MARK: - NSWindowDelegate
extension LogViewerWindowController: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        logger.info("Log viewer window closed", service: serviceName)
    }
}
