import Foundation
import SwiftUI

/// Manages the storage and filtering of log entries
class LogStore: ObservableObject {
    static let shared = LogStore()

    // MARK: - Published Properties

    /// All log entries captured
    @Published private(set) var allLogs: [LogEntry] = []

    /// Currently selected log level filter
    @Published var selectedLogLevel: LogLevelFilter = .all {
        didSet {
            filterLogs()
        }
    }

    /// Currently selected feature category filter
    @Published var selectedCategory: LogCategoryFilter = .all {
        didSet {
            filterLogs()
        }
    }

    /// Filtered logs based on current filter settings
    @Published private(set) var filteredLogs: [LogEntry] = []

    // MARK: - Private Properties

    /// Maximum number of logs to store
    private let maxLogCount = 10000

    /// Logger for this class
    private let logger = LoggingService.shared
    private let serviceName = "LogStore"

    // MARK: - Initialization

    private init() {
        logger.info("LogStore initialized", service: serviceName)
    }

    // MARK: - Log Management

    /// Add a new log entry
    func addLog(message: String, level: LogLevel, service: String, category: LogCategory) {
        let entry = LogEntry(
            timestamp: Date(),
            message: message,
            level: level,
            service: service,
            category: category
        )

        // Special handling for workspace preview logs removed

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // Add to all logs
            self.allLogs.append(entry)

            // Trim if needed
            if self.allLogs.count > self.maxLogCount {
                self.allLogs.removeFirst(self.allLogs.count - self.maxLogCount)
            }

            // Update filtered logs if this entry passes the current filters
            if self.logMatchesFilters(entry) {
                self.filteredLogs.append(entry)
            }
        }
    }

    /// Clear all logs
    func clearLogs() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.allLogs.removeAll()
            self.filteredLogs.removeAll()
        }
    }

    // MARK: - Filtering

    /// Apply current filters to logs
    private func filterLogs() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.filteredLogs = self.allLogs.filter { self.logMatchesFilters($0) }
        }
    }

    /// Check if a log entry matches the current filters
    private func logMatchesFilters(_ log: LogEntry) -> Bool {
        // Filter by log level
        let matchesLevel: Bool
        switch selectedLogLevel {
        case .all:
            matchesLevel = true
        case .debug:
            matchesLevel = log.level == .debug
        case .info:
            matchesLevel = log.level == .info
        case .warning:
            matchesLevel = log.level == .warning
        case .error:
            matchesLevel = log.level == .error
        }

        // Filter by category
        let matchesCategory: Bool
        switch selectedCategory {
        case .all:
            matchesCategory = true
        case .feature(let featureGroup):
            // Check if the log's category is in the selected feature group
            if let categories = LogCategory.featureGroups[featureGroup] {
                matchesCategory = categories.contains(log.category)
            } else {
                matchesCategory = false
            }
        case .specific(let category):
            matchesCategory = log.category == category
        }

        return matchesLevel && matchesCategory
    }

    /// Get logs grouped by feature
    func getLogsGroupedByFeature() -> [String: [LogEntry]] {
        var groupedLogs: [String: [LogEntry]] = [:]

        // Group logs by their feature group
        for log in filteredLogs {
            // Find which feature group this category belongs to
            var featureGroup = "Other"
            for (group, categories) in LogCategory.featureGroups {
                if categories.contains(log.category) {
                    featureGroup = group
                    break
                }
            }

            // Add to the appropriate group
            if groupedLogs[featureGroup] == nil {
                groupedLogs[featureGroup] = []
            }
            groupedLogs[featureGroup]?.append(log)
        }

        return groupedLogs
    }
}

/// Enum for log level filtering options
enum LogLevelFilter: String, CaseIterable, Identifiable {
    case all = "All"
    case debug = "Debug"
    case info = "Info"
    case warning = "Warning"
    case error = "Error"

    var id: String { self.rawValue }
}

/// Enum for category filtering options
enum LogCategoryFilter: Equatable, Identifiable {
    case all
    case feature(String)
    case specific(LogCategory)

    var id: String {
        switch self {
        case .all:
            return "all"
        case .feature(let name):
            return "feature-\(name)"
        case .specific(let category):
            return "category-\(category.rawValue)"
        }
    }

    var displayName: String {
        switch self {
        case .all:
            return "All Categories"
        case .feature(let name):
            return name
        case .specific(let category):
            return category.displayName
        }
    }

    static func == (lhs: LogCategoryFilter, rhs: LogCategoryFilter) -> Bool {
        return lhs.id == rhs.id
    }
}
