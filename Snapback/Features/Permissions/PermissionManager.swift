import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    /// Logger for diagnostic purposes
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    /// Counter for tracking permission checks
    private var permissionCheckCount: Int = 0

    private init() {
        logger.info("🔍 PERMISSION DIAGNOSTIC: PermissionManager initializing", service: serviceName)

        // Check initial permission status
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Performing initial permission check", service: serviceName)
        isAccessibilityPermissionGranted = checkAccessibilityPermission()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Initial permission status set to: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // Set up timer to check permissions periodically
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Setting up periodic permission check timer (1.0s interval)",
            service: serviceName)
        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: 1.0,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager initialization complete",
            service: serviceName)
    }

    /// Check if accessibility permissions are granted
    func checkAccessibilityPermission() -> Bool {
        permissionCheckCount += 1
        let timestamp = Date()

        logger.debug(
            "🔍 PERMISSION DIAGNOSTIC: About to call AXIsProcessTrusted() - Check #\(permissionCheckCount) at \(timestamp)",
            service: serviceName)

        let result = AXIsProcessTrusted()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: AXIsProcessTrusted() returned: \(result) - Check #\(permissionCheckCount) at \(timestamp)",
            service: serviceName)

        return result
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let timestamp = Date()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: requestAccessibilityPermission() called at \(timestamp)",
            service: serviceName)
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Current permission status before showing dialog: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "Snapback needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: About to show permission dialog", service: serviceName)
        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Open Settings' button", service: serviceName
            )
            if let url = URL(
                string:
                    "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
            {
                NSWorkspace.shared.open(url)
                logger.info(
                    "🔍 PERMISSION DIAGNOSTIC: Opened System Settings for accessibility permissions",
                    service: serviceName)
            } else {
                logger.error(
                    "🔍 PERMISSION DIAGNOSTIC: Failed to create URL for System Settings",
                    service: serviceName)
            }
        } else {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Cancel' button or dismissed dialog",
                service: serviceName)
        }

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Permission dialog interaction complete", service: serviceName)
    }

    /// Periodically check permission status
    @objc private func checkPermissionStatus() {
        logger.debug(
            "🔍 PERMISSION DIAGNOSTIC: Periodic permission check triggered", service: serviceName)
        logger.debug(
            "🔍 PERMISSION DIAGNOSTIC: Current stored permission status: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        let currentStatus = checkAccessibilityPermission()
        logger.debug(
            "🔍 PERMISSION DIAGNOSTIC: Fresh permission check result: \(currentStatus)",
            service: serviceName)

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status CHANGED from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                service: serviceName)
            isAccessibilityPermissionGranted = currentStatus

            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Posting permission status change notification with value: \(isAccessibilityPermissionGranted)",
                service: serviceName)
            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status change notification posted",
                service: serviceName)
        } else {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Permission status unchanged (\(currentStatus))",
                service: serviceName)
        }
    }

    deinit {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager deinitializing", service: serviceName)
        permissionCheckTimer?.invalidate()
    }
}
