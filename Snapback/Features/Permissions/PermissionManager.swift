import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    /// Logger for diagnostic purposes
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    /// Counter for tracking permission checks
    private var permissionCheckCount: Int = 0

    /// Adaptive timing configuration
    private let checkIntervalWhenDenied: TimeInterval = 3.0  // Check every 3 seconds when denied
    private let checkIntervalWhenGranted: TimeInterval = 30.0  // Check every 30 seconds when granted
    private let maxChecksWhenGranted: Int = 5  // Stop frequent checking after 5 successful checks
    private var successfulGrantedChecks: Int = 0

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let hasEverBeenGranted = "SnapbackAccessibilityPermissionEverGranted"
        static let lastKnownStatus = "SnapbackLastKnownAccessibilityStatus"
        static let lastCheckTimestamp = "SnapbackLastPermissionCheckTimestamp"
    }

    private init() {
        logger.info("🔍 PERMISSION DIAGNOSTIC: PermissionManager initializing", service: serviceName)

        // Check persistent state first
        let hasEverBeenGranted = UserDefaults.standard.bool(
            forKey: PersistenceKeys.hasEverBeenGranted)
        let lastKnownStatus = UserDefaults.standard.bool(forKey: PersistenceKeys.lastKnownStatus)
        let lastCheckTimestamp = UserDefaults.standard.double(
            forKey: PersistenceKeys.lastCheckTimestamp)

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent state - Ever granted: \(hasEverBeenGranted), Last known: \(lastKnownStatus), Last check: \(Date(timeIntervalSince1970: lastCheckTimestamp))",
            service: serviceName)

        // FIXED: Always perform fresh check on app launch to catch permission revocations
        // This ensures we don't rely solely on cached state which can be stale
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Performing fresh permission check on app launch",
            service: serviceName)

        isAccessibilityPermissionGranted = checkAccessibilityPermission()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Fresh check result: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // Set up adaptive timer based on actual permission status
        startAdaptiveTimer()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager initialization complete",
            service: serviceName)
    }

    /// Update persistent state when permissions change
    private func updatePersistentState(_ isGranted: Bool) {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Updating persistent state to: \(isGranted)",
            service: serviceName)

        // Update last known status
        UserDefaults.standard.set(isGranted, forKey: PersistenceKeys.lastKnownStatus)

        // Update timestamp
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastCheckTimestamp)

        // If permissions are granted for the first time, mark it
        if isGranted && !UserDefaults.standard.bool(forKey: PersistenceKeys.hasEverBeenGranted) {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permissions granted for the first time - marking as ever granted",
                service: serviceName)
            UserDefaults.standard.set(true, forKey: PersistenceKeys.hasEverBeenGranted)
        }

        // Force synchronization to ensure persistence
        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent state updated and synchronized",
            service: serviceName)
    }

    /// Start adaptive timer based on current permission status
    private func startAdaptiveTimer() {
        permissionCheckTimer?.invalidate()

        let interval: TimeInterval
        let reason: String

        if isAccessibilityPermissionGranted {
            if successfulGrantedChecks >= maxChecksWhenGranted {
                // Stop frequent checking after permissions have been stable
                interval = 120.0  // Check every 2 minutes
                reason = "permissions stable (2min interval)"
            } else {
                interval = checkIntervalWhenGranted
                reason = "permissions granted (30s interval)"
            }
        } else {
            interval = checkIntervalWhenDenied
            reason = "permissions denied (3s interval)"
            successfulGrantedChecks = 0  // Reset counter when denied
        }

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Starting adaptive timer - \(reason)",
            service: serviceName)

        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: interval,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )
    }

    /// Check if accessibility permissions are granted
    func checkAccessibilityPermission() -> Bool {
        permissionCheckCount += 1
        let timestamp = Date()

        // Reduce logging frequency - only log every 10th check when permissions are granted
        let shouldLogDetails = !isAccessibilityPermissionGranted || (permissionCheckCount % 10 == 0)

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: About to call AXIsProcessTrusted() - Check #\(permissionCheckCount) at \(timestamp)",
                service: serviceName)
        }

        // SANDBOX FIX: Check if we're running in a sandboxed environment
        let isSandboxed = ProcessInfo.processInfo.environment["APP_SANDBOX_CONTAINER_ID"] != nil

        if shouldLogDetails && isSandboxed {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Running in sandboxed environment - using enhanced permission check",
                service: serviceName)
        }

        // For sandboxed apps, we need to use a different approach
        let result: Bool
        if isSandboxed {
            // In sandbox, use the standard check but with additional validation
            result = checkSandboxedAccessibilityPermission()
        } else {
            // Non-sandboxed apps use the standard approach
            result = AXIsProcessTrusted()
        }

        if shouldLogDetails || result != isAccessibilityPermissionGranted {
            // Always log when status might change, or periodically when stable
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission check returned: \(result) - Check #\(permissionCheckCount) at \(timestamp) (Sandboxed: \(isSandboxed))",
                service: serviceName)
        }

        // FIXED: Enhanced logging for app launch scenarios
        if permissionCheckCount == 1 {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: FIRST CHECK ON APP LAUNCH - Permission = \(result) (Sandboxed: \(isSandboxed))",
                service: serviceName)
        }

        // Update persistent state whenever we get a fresh result
        updatePersistentState(result)

        return result
    }

    /// Enhanced permission check for sandboxed environments
    private func checkSandboxedAccessibilityPermission() -> Bool {
        // First, try the standard check
        let standardResult = AXIsProcessTrusted()

        // In sandboxed environments, we also need to verify we can actually use accessibility APIs
        if standardResult {
            // Test if we can actually create accessibility elements
            let testResult = testAccessibilityFunctionality()
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Sandbox accessibility test - Standard: \(standardResult), Functional: \(testResult)",
                service: serviceName)
            return testResult
        }

        return standardResult
    }

    /// Test if accessibility functionality actually works (for sandboxed apps)
    private func testAccessibilityFunctionality() -> Bool {
        // Try to get the frontmost application
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Cannot get frontmost application for accessibility test",
                service: serviceName)
            return false
        }

        // Try to create an accessibility element for the frontmost app
        let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)

        // Try to get a basic attribute to test if accessibility is working
        var windowsRef: CFTypeRef?
        let error = AXUIElementCopyAttributeValue(
            appElement, kAXWindowsAttribute as CFString, &windowsRef)

        let isWorking = error == .success

        if !isWorking {
            logger.warning(
                "🔍 PERMISSION DIAGNOSTIC: Accessibility API test failed with error: \(error)",
                service: serviceName)
        }

        return isWorking
    }

    /// Force TCC registration by making an actual accessibility API call
    /// This triggers the system to add the app to the TCC database
    private func forceTCCRegistration() -> Bool {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Attempting to force TCC registration",
            service: serviceName)

        // Method 1: Call AXIsProcessTrusted with prompt
        let promptResult = AXIsProcessTrusted()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: AXIsProcessTrusted() = \(promptResult)",
            service: serviceName)

        // Method 2: Try to access system-wide accessibility element
        let systemWideElement = AXUIElementCreateSystemWide()
        var focusedAppRef: CFTypeRef?
        let focusedAppError = AXUIElementCopyAttributeValue(
            systemWideElement, kAXFocusedApplicationAttribute as CFString, &focusedAppRef)

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: System-wide element access error: \(focusedAppError)",
            service: serviceName)

        // Method 3: Try to get running applications and access their windows
        let runningApps = NSWorkspace.shared.runningApplications
        for app in runningApps.prefix(3) {  // Try first 3 apps
            if app.processIdentifier > 0 {
                let appElement = AXUIElementCreateApplication(app.processIdentifier)
                var windowsRef: CFTypeRef?
                let windowsError = AXUIElementCopyAttributeValue(
                    appElement, kAXWindowsAttribute as CFString, &windowsRef)

                logger.debug(
                    "🔍 PERMISSION DIAGNOSTIC: App \(app.localizedName ?? "Unknown") windows access: \(windowsError)",
                    service: serviceName)

                // If we get success or a permission-related error, we've triggered TCC
                if windowsError == .success || windowsError == .apiDisabled
                    || windowsError == .notImplemented
                {
                    logger.info(
                        "🔍 PERMISSION DIAGNOSTIC: Successfully triggered TCC registration via app \(app.localizedName ?? "Unknown")",
                        service: serviceName)
                    return true
                }
            }
        }

        return promptResult
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let timestamp = Date()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: requestAccessibilityPermission() called at \(timestamp)",
            service: serviceName)
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Current permission status before showing dialog: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // Check if we're running in a sandboxed environment
        let isSandboxed = ProcessInfo.processInfo.environment["APP_SANDBOX_CONTAINER_ID"] != nil

        // SANDBOX FIX: Force TCC registration by making an actual accessibility API call
        // This triggers the system to register the app in the TCC database
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Forcing TCC registration with accessibility API call",
            service: serviceName)

        let forceRegistrationResult = forceTCCRegistration()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Force registration result: \(forceRegistrationResult)",
            service: serviceName)

        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"

        // Provide different instructions based on sandbox status
        if isSandboxed {
            alert.informativeText = """
                Snapback needs Accessibility permissions to manage window positions.

                For sandboxed apps:
                1. Click "Open Settings" below
                2. Look for "Snapback" in the Accessibility list (should appear now)
                3. Toggle the switch to enable access
                4. If Snapback still doesn't appear, restart this app and try again

                Note: The app has just triggered system registration - Snapback should now be visible in Settings.
                """
        } else {
            alert.informativeText = """
                Snapback needs Accessibility permissions to manage window positions.

                Please grant access in:
                System Settings > Privacy & Security > Accessibility

                Look for "Snapback" in the list and toggle the switch to enable access.
                """
        }

        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: About to show permission dialog (Sandboxed: \(isSandboxed))",
            service: serviceName)
        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Open Settings' button", service: serviceName
            )

            // Try multiple URL schemes for better compatibility
            let settingsURLs = [
                "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility",
                "x-apple.systempreferences:com.apple.preference.security",
                "x-apple.systempreferences:",
            ]

            var opened = false
            for urlString in settingsURLs {
                if let url = URL(string: urlString) {
                    if NSWorkspace.shared.open(url) {
                        logger.info(
                            "🔍 PERMISSION DIAGNOSTIC: Successfully opened System Settings with URL: \(urlString)",
                            service: serviceName)
                        opened = true
                        break
                    }
                }
            }

            if !opened {
                logger.error(
                    "🔍 PERMISSION DIAGNOSTIC: Failed to open System Settings with any URL scheme",
                    service: serviceName)

                // Fallback: Show additional guidance
                let fallbackAlert = NSAlert()
                fallbackAlert.messageText = "Manual Setup Required"
                fallbackAlert.informativeText = """
                    Please manually open:
                    System Settings > Privacy & Security > Accessibility

                    Snapback should now appear in the list after the permission request.
                    """
                fallbackAlert.runModal()
            }

            // SANDBOX FIX: Schedule a permission re-check after user has time to grant permissions
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
                self?.logger.info(
                    "🔍 PERMISSION DIAGNOSTIC: Performing delayed permission re-check",
                    service: self?.serviceName ?? "PermissionManager")
                let _ = self?.forceFreshPermissionCheck()
            }
        } else {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Cancel' button or dismissed dialog",
                service: serviceName)
        }

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Permission dialog interaction complete", service: serviceName)
    }

    /// Periodically check permission status with adaptive timing
    @objc private func checkPermissionStatus() {
        // Reduce logging frequency - only log every 10th check when permissions are granted
        let shouldLogDetails = !isAccessibilityPermissionGranted || (permissionCheckCount % 10 == 0)

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Periodic permission check triggered (check #\(permissionCheckCount))",
                service: serviceName)
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Current stored permission status: \(isAccessibilityPermissionGranted)",
                service: serviceName)
        }

        let currentStatus = checkAccessibilityPermission()

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Fresh permission check result: \(currentStatus)",
                service: serviceName)
        }

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status CHANGED from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                service: serviceName)

            isAccessibilityPermissionGranted = currentStatus

            // Update persistent state when status changes
            updatePersistentState(currentStatus)

            // Update adaptive timing based on new status
            if currentStatus {
                successfulGrantedChecks = 1  // Start counting successful checks
            } else {
                successfulGrantedChecks = 0  // Reset when denied
            }

            // Restart timer with new interval if status changed
            startAdaptiveTimer()

            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Posting permission status change notification with value: \(isAccessibilityPermissionGranted)",
                service: serviceName)
            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status change notification posted",
                service: serviceName)
        } else if currentStatus {
            // Permission is granted and unchanged - increment successful check counter
            successfulGrantedChecks += 1

            // If we've reached the threshold, restart timer with longer interval
            if successfulGrantedChecks == maxChecksWhenGranted {
                logger.info(
                    "🔍 PERMISSION DIAGNOSTIC: Permissions stable after \(maxChecksWhenGranted) checks - switching to longer interval",
                    service: serviceName)
                startAdaptiveTimer()
            }
        }

        // Only log unchanged status occasionally to reduce noise
        if shouldLogDetails && currentStatus == isAccessibilityPermissionGranted {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Permission status unchanged (\(currentStatus))",
                service: serviceName)
        }
    }

    /// Manually trigger a permission check (useful when app becomes active)
    func triggerPermissionCheck() {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Manual permission check triggered", service: serviceName)
        checkPermissionStatus()
    }

    /// Get current timer interval for debugging
    func getCurrentTimerInterval() -> TimeInterval? {
        return permissionCheckTimer?.timeInterval
    }

    /// Clear persistent permission state (useful for testing)
    func clearPersistentState() {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Clearing persistent permission state",
            service: serviceName)

        UserDefaults.standard.removeObject(forKey: PersistenceKeys.hasEverBeenGranted)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastKnownStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastCheckTimestamp)
        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent permission state cleared",
            service: serviceName)
    }

    /// Get persistent state for debugging
    func getPersistentState() -> (
        hasEverBeenGranted: Bool, lastKnownStatus: Bool, lastCheckTimestamp: TimeInterval
    ) {
        return (
            hasEverBeenGranted: UserDefaults.standard.bool(
                forKey: PersistenceKeys.hasEverBeenGranted),
            lastKnownStatus: UserDefaults.standard.bool(forKey: PersistenceKeys.lastKnownStatus),
            lastCheckTimestamp: UserDefaults.standard.double(
                forKey: PersistenceKeys.lastCheckTimestamp)
        )
    }

    /// Force a fresh permission check (useful for testing and debugging)
    func forceFreshPermissionCheck() -> Bool {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Force fresh permission check requested",
            service: serviceName)

        let previousState = isAccessibilityPermissionGranted
        let freshResult = checkAccessibilityPermission()

        if freshResult != previousState {
            logger.warning(
                "🔍 PERMISSION DIAGNOSTIC: Force check detected state change from \(previousState) to \(freshResult)",
                service: serviceName)

            // Update our state and notify
            isAccessibilityPermissionGranted = freshResult

            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )

            // Restart timer with appropriate interval
            startAdaptiveTimer()
        }

        return freshResult
    }

    deinit {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager deinitializing", service: serviceName)
        permissionCheckTimer?.invalidate()
    }
}
