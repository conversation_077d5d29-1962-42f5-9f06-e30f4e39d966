import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import SwiftUI

/// A reusable form component for saving and editing workspaces
struct WorkspaceFormView: View {
    // MARK: - Properties

    @EnvironmentObject var workspaceService: WorkspaceService
    @Environment(\.dismiss) var dismiss

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceFormView"

    // Form State
    @Binding var workspaceName: String
    @Binding var customLayoutName: String
    @FocusState private var isNameFieldFocused: Bool

    // App selection state
    @Binding var appItems: [AppSelectionItem]
    @Binding var selectedCount: Int

    // Shortcut Recording State
    @Binding var shortcutDisplayString: String
    @Binding var recordedKeyCode: UInt16?
    @Binding var recordedModifiers: UInt?

    // Mode
    let isEditMode: Bool
    let existingWorkspace: Workspace?

    // Callbacks
    var onSave: () -> Void
    var onCancel: () -> Void
    var onUpdatePositions: () -> Void

    // MARK: - Display Arrangement Detection

    /// Check if the current display arrangement differs from the saved one
    private var hasDisplayArrangementChanged: Bool {
        guard isEditMode, let workspace = existingWorkspace,
            let savedArrangement = workspace.displayArrangement
        else {
            return false
        }

        // Get current display arrangement
        let currentDisplays = WindowLayoutManager.getAllDisplays()
        let currentDisplayIDs = Set(currentDisplays.map { $0.id })
        let savedDisplayIDs = Set(savedArrangement.displayIDs)

        // Debug logging
        logger.debug("=== DISPLAY ARRANGEMENT COMPARISON ===", service: serviceName)
        logger.debug("Current display IDs: \(currentDisplayIDs)", service: serviceName)
        logger.debug("Saved display IDs: \(savedDisplayIDs)", service: serviceName)

        // Check if display IDs are different
        if currentDisplayIDs != savedDisplayIDs {
            logger.debug("❌ Display IDs differ - arrangement changed", service: serviceName)
            return true
        }

        // Check if main display has changed
        let currentMainDisplay = currentDisplays.first { $0.isMain }
        logger.debug("Current main display: \(currentMainDisplay?.id ?? 0)", service: serviceName)
        logger.debug("Saved main display: \(savedArrangement.mainDisplayID)", service: serviceName)

        if currentMainDisplay?.id != savedArrangement.mainDisplayID {
            logger.debug("❌ Main display changed - arrangement changed", service: serviceName)
            return true
        }

        // Check if display positions have changed significantly
        for display in currentDisplays {
            if let savedFrame = savedArrangement.displayFrames[display.id] {
                let currentFrame = display.logicalFrame
                let xDiff = abs(currentFrame.origin.x - savedFrame.origin.x)
                let yDiff = abs(currentFrame.origin.y - savedFrame.origin.y)

                logger.debug("Display \(display.id):", service: serviceName)
                logger.debug("  Current: \(currentFrame)", service: serviceName)
                logger.debug("  Saved: \(savedFrame)", service: serviceName)
                logger.debug("  X diff: \(xDiff), Y diff: \(yDiff)", service: serviceName)

                // Consider positions changed if they differ by more than 100 pixels
                // This accounts for minor positioning differences when rearranging displays
                logger.debug(
                    "Checking tolerance: xDiff=\(xDiff), yDiff=\(yDiff), tolerance=100",
                    service: serviceName)
                if xDiff > 100 || yDiff > 100 {
                    logger.debug(
                        "❌ Display \(display.id) position changed significantly (>\(100)px) - arrangement changed",
                        service: serviceName)
                    return true
                } else {
                    logger.debug(
                        "✅ Display \(display.id) position within tolerance (≤\(100)px)",
                        service: serviceName)
                }
            }
        }

        logger.debug("✅ Display arrangement matches saved arrangement", service: serviceName)
        return false
    }

    // MARK: - Initializers

    /// Initialize for creating a new workspace
    init(
        workspaceName: Binding<String>,
        customLayoutName: Binding<String>,
        appItems: Binding<[AppSelectionItem]>,
        selectedCount: Binding<Int>,
        shortcutDisplayString: Binding<String>,
        recordedKeyCode: Binding<UInt16?>,
        recordedModifiers: Binding<UInt?>,
        onSave: @escaping () -> Void,
        onCancel: @escaping () -> Void,
        onUpdatePositions: @escaping () -> Void
    ) {
        self._workspaceName = workspaceName
        self._customLayoutName = customLayoutName
        self._appItems = appItems
        self._selectedCount = selectedCount
        self._shortcutDisplayString = shortcutDisplayString
        self._recordedKeyCode = recordedKeyCode
        self._recordedModifiers = recordedModifiers
        self.isEditMode = false
        self.existingWorkspace = nil
        self.onSave = onSave
        self.onCancel = onCancel
        self.onUpdatePositions = onUpdatePositions
    }

    /// Initialize for editing an existing workspace
    init(
        workspace: Workspace,
        workspaceName: Binding<String>,
        customLayoutName: Binding<String>,
        appItems: Binding<[AppSelectionItem]>,
        selectedCount: Binding<Int>,
        shortcutDisplayString: Binding<String>,
        recordedKeyCode: Binding<UInt16?>,
        recordedModifiers: Binding<UInt?>,
        onSave: @escaping () -> Void,
        onCancel: @escaping () -> Void,
        onUpdatePositions: @escaping () -> Void
    ) {
        self._workspaceName = workspaceName
        self._customLayoutName = customLayoutName
        self._appItems = appItems
        self._selectedCount = selectedCount
        self._shortcutDisplayString = shortcutDisplayString
        self._recordedKeyCode = recordedKeyCode
        self._recordedModifiers = recordedModifiers
        self.isEditMode = true
        self.existingWorkspace = workspace
        self.onSave = onSave
        self.onCancel = onCancel
        self.onUpdatePositions = onUpdatePositions
    }

    // MARK: - View Body

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text(isEditMode ? "Edit Workspace" : "Save Workspace")
                    .font(.system(size: SnapbackTheme.FontSize.title, weight: .semibold))
                    .padding(.top, 20)
                    .padding(.bottom, 10)

                Spacer()
            }
            .padding(.horizontal, 20)

            // Main content
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Basic info section
                    VStack(alignment: .leading, spacing: 15) {
                        // Workspace Name and Shortcut in the same row
                        Text("Workspace Information")
                            .snapbackSectionTitleStyle()

                        // Card containing inputs and description
                        VStack(alignment: .leading, spacing: 10) {
                            // Inputs row
                            HStack(alignment: .center, spacing: 15) {
                                // Name input
                                TextField("Enter workspace name", text: $workspaceName)
                                    .textFieldStyle(.plain)
                                    .frame(maxWidth: .infinity)
                                    .focused($isNameFieldFocused)

                                // Shortcut recorder (no label)
                                if let id = existingWorkspace?.id, isEditMode {
                                    // For edit mode, use the workspace's ID for the shortcut
                                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(
                                        for: id)
                                    KeyboardShortcuts.Recorder(for: shortcutName)
                                        .frame(maxWidth: .infinity, alignment: .trailing)
                                } else {
                                    // For save mode, use a temporary shortcut
                                    let tempShortcutName = KeyboardShortcuts.Name(
                                        "temp_workspace_shortcut")
                                    KeyboardShortcuts.Recorder(for: tempShortcutName)
                                        .frame(maxWidth: .infinity, alignment: .trailing)
                                }
                            }

                            // Description text
                            Text(
                                "Enter a name for your workspace and optionally assign a keyboard shortcut for quick access."
                            )
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(15)
                        .background(SnapbackTheme.Background.card)
                        .cornerRadius(SnapbackTheme.CornerRadius.card)
                    }

                    // Preview section
                    VStack(alignment: .leading, spacing: 15) {
                        HStack {
                            Text("Preview")
                                .snapbackSectionTitleStyle()

                            Spacer()

                            // Update Positions button
                            Button(action: onUpdatePositions) {
                                HStack(spacing: SnapbackTheme.Padding.small) {
                                    Image(systemName: "arrow.clockwise")
                                        .font(.system(size: SnapbackTheme.FontSize.caption))
                                    Text("Update Positions")
                                        .font(.system(size: SnapbackTheme.FontSize.body))
                                }
                                .padding(.horizontal, SnapbackTheme.Padding.vertical)
                                .padding(.vertical, SnapbackTheme.Padding.small)
                            }
                            .buttonStyle(.plain)
                            .background(SnapbackTheme.Background.card)
                            .cornerRadius(SnapbackTheme.CornerRadius.card)
                        }

                        // Workspace preview
                        WorkspacePreview(
                            windowInfos: appItems.filter { $0.isSelected }.map { $0.windowInfo },
                            onWindowClose: { closedWindowInfo in
                                // Find the app item that corresponds to the closed window
                                if let index = appItems.firstIndex(where: {
                                    $0.windowInfo.appBundleIdentifier
                                        == closedWindowInfo.appBundleIdentifier
                                        && $0.windowInfo.frame.origin.x
                                            == closedWindowInfo.frame.origin.x
                                        && $0.windowInfo.frame.origin.y
                                            == closedWindowInfo.frame.origin.y
                                }) {
                                    // Set the app item to not selected
                                    appItems[index].isSelected = false

                                    // Update the selected count
                                    selectedCount = appItems.filter { $0.isSelected }.count

                                    logger.debug(
                                        "Window closed from preview: \(appItems[index].appName), Selected count: \(selectedCount)",
                                        service: serviceName,
                                        category: .userInterface
                                    )
                                }
                            }
                        )
                        .frame(height: 220)
                        .frame(maxWidth: .infinity)
                        .cornerRadius(SnapbackTheme.CornerRadius.card)
                        .padding(15)
                        .background(SnapbackTheme.Background.card)
                        .cornerRadius(SnapbackTheme.CornerRadius.card)
                    }

                    // Display arrangement warning (only show in edit mode if arrangement changed)
                    if hasDisplayArrangementChanged {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack(spacing: 8) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.system(size: 16))

                                Text("Display Settings Changed")
                                    .font(
                                        .system(
                                            size: SnapbackTheme.FontSize.body, weight: .semibold)
                                    )
                                    .foregroundColor(.primary)
                            }

                            Text(
                                "Your display settings have changed since this workspace was created. We'll do our best to restore windows to appropriate positions, but some adjustments may be needed. If the preview above doesn't look right, click \"Update Positions\" to capture the current window layout."
                            )
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(15)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(SnapbackTheme.CornerRadius.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                                .strokeBorder(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                    }

                    // App selection section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Applications")
                            .snapbackSectionTitleStyle()

                        // Description text
                        Text(
                            "Select which applications to include in this workspace. Only selected applications will be restored when you activate this workspace."
                        )
                        .font(.system(size: SnapbackTheme.FontSize.caption))
                        .foregroundColor(SnapbackTheme.Text.secondary)
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.bottom, 5)

                        // Clean, modern list style with adaptive scrolling
                        VStack(spacing: 0) {
                            // Calculate if we need scrolling based on number of items
                            // Each row is approximately 40 pixels high
                            let rowHeight: CGFloat = 40
                            let maxRowsBeforeScrolling = 6
                            let shouldScroll = appItems.count > maxRowsBeforeScrolling
                            let contentHeight =
                                shouldScroll
                                ? rowHeight * CGFloat(maxRowsBeforeScrolling)
                                : rowHeight * CGFloat(appItems.count)

                            // Use ScrollView conditionally based on item count
                            Group {
                                if shouldScroll {
                                    ScrollView {
                                        appListContent
                                    }
                                    .frame(height: contentHeight)
                                } else {
                                    appListContent
                                }
                            }
                        }
                        .background(SnapbackTheme.Background.card)
                        .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
                        .overlay(
                            RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                                .strokeBorder(
                                    SnapbackTheme.Border.card,
                                    lineWidth: SnapbackTheme.BorderWidth.normal
                                )
                        )

                        HStack {
                            Text(
                                "Selected: \(selectedCount) of \(appItems.count) apps"
                            )
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)

                            Spacer()

                            Button("Select All") {
                                // Set all items to selected
                                for index in appItems.indices {
                                    appItems[index].isSelected = true
                                }

                                // Update the selected count
                                selectedCount = appItems.count
                                logger.debug(
                                    "Select All pressed: \(selectedCount) apps selected",
                                    service: serviceName,
                                    category: .userInterface
                                )
                            }
                            .buttonStyle(.borderless)
                            .font(.caption)

                            Button("Deselect All") {
                                // Set all items to deselected
                                for index in appItems.indices {
                                    appItems[index].isSelected = false
                                }

                                // Update the selected count
                                selectedCount = 0
                                logger.debug(
                                    "Deselect All pressed: \(selectedCount) apps selected",
                                    service: serviceName,
                                    category: .userInterface
                                )
                            }
                            .buttonStyle(.borderless)
                            .font(.caption)
                        }
                        .padding(.top, 4)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
            }

            // Footer with buttons using SettingsFooter for consistency
            SettingsFooter {
                HStack(spacing: SnapbackTheme.Padding.standard) {
                    Spacer()

                    Button("Cancel", action: onCancel)
                        .keyboardShortcut(.cancelAction)
                        .padding(.vertical, SnapbackTheme.Padding.vertical)
                        .padding(.horizontal, SnapbackTheme.Padding.horizontal)

                    Button(isEditMode ? "Save Changes" : "Save", action: onSave)
                        .keyboardShortcut(.defaultAction)
                        .padding(.vertical, SnapbackTheme.Padding.vertical)
                        .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                        .disabled(
                            workspaceName.isEmpty
                                || (!appItems.isEmpty && appItems.filter { $0.isSelected }.isEmpty)
                        )
                }
            }
        }
        .onAppear {
            // Set focus on the name field
            isNameFieldFocused = true
        }
    }

    // MARK: - App Row Views

    // Row view for edit mode
    struct EditWorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]

        var body: some View {
            HStack(spacing: 12) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count
                            print(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)"
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .snapbackRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                print(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)"
                )
            }
        }
    }

    // Row view for save mode
    struct WorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]
        let logger: LoggingService
        let serviceName: String

        var body: some View {
            HStack(spacing: 12) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count

                            logger.debug(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)",
                                service: serviceName,
                                category: .userInterface
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .snapbackRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                logger.debug(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)",
                    service: serviceName,
                    category: .userInterface
                )
            }
        }
    }

    // MARK: - App List Content

    // Update the app list content
    private var appListContent: some View {
        VStack(spacing: 0) {
            ForEach(appItems.indices, id: \.self) { index in
                if isEditMode {
                    EditWorkspaceAppRow(
                        appItem: $appItems[index],
                        selectedCount: $selectedCount,
                        appItems: appItems
                    )
                    .background(
                        index % 2 == 0 ? Color.clear : Color.gray.opacity(0.05)
                    )
                } else {
                    WorkspaceAppRow(
                        appItem: $appItems[index],
                        selectedCount: $selectedCount,
                        appItems: appItems,
                        logger: logger,
                        serviceName: serviceName
                    )
                    .background(
                        index % 2 == 0 ? Color.clear : Color.gray.opacity(0.05)
                    )
                }
            }
        }
    }
}

// MARK: - Helper for vertical arrangement detection
extension WorkspaceFormView {
    func isVerticalArrangement(screens: [NSScreen]) -> Bool {
        // Handle single screen case
        if screens.count <= 1 {
            return false
        }

        // Calculate the total bounds of all screens
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if screens are stacked vertically
        var hasVerticalStacking = false

        // For macOS, we need to be careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            // Handle two screens case
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Check if one screen is completely above the other
            hasVerticalStacking =
                (screen1.frame.minY >= screen2.frame.maxY)
                || (screen2.frame.minY >= screen1.frame.maxY)
        } else {
            // Handle more than two screens case
            // For more than 2 screens, check if any screen is positioned below another screen
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        return (totalHeight > totalWidth * 1.2) || hasVerticalStacking
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var workspaceName = "My Workspace"
        @State var customLayoutName = ""
        @State var appItems: [AppSelectionItem] = [
            AppSelectionItem(
                windowInfo: WindowInfo(
                    frame: CGRect(x: 100, y: 100, width: 800, height: 600),
                    monitorID: UUID(),
                    appBundleIdentifier: "com.apple.Safari",
                    isFullscreen: false
                )
            ),
            AppSelectionItem(
                windowInfo: WindowInfo(
                    frame: CGRect(x: 1500, y: 200, width: 700, height: 500),
                    monitorID: UUID(),
                    appBundleIdentifier: "com.apple.mail",
                    isFullscreen: false
                )
            ),
        ]
        @State var selectedCount = 2
        @State var shortcutDisplayString = "⌘S"
        @State var recordedKeyCode: UInt16? = UInt16(kVK_ANSI_S)
        @State var recordedModifiers: UInt? = NSEvent.ModifierFlags.command.rawValue

        var body: some View {
            WorkspaceFormView(
                workspaceName: $workspaceName,
                customLayoutName: $customLayoutName,
                appItems: $appItems,
                selectedCount: $selectedCount,
                shortcutDisplayString: $shortcutDisplayString,
                recordedKeyCode: $recordedKeyCode,
                recordedModifiers: $recordedModifiers,
                onSave: { print("Save clicked") },
                onCancel: { print("Cancel clicked") },
                onUpdatePositions: { print("Update positions clicked") }
            )
            .environmentObject(WorkspaceService(snappingService: WindowSnappingService()))
            .frame(width: 600, height: 800)
        }
    }

    return PreviewWrapper()
}
