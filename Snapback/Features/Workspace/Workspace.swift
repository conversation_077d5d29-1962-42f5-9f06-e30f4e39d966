import AppKit
import Carbon
import CoreGraphics
import Foundation
import KeyboardShortcuts

// MARK: - Main Structs
public struct WindowInfo: Codable {
    public let frame: CGRect
    public let monitorID: UUID?
    public let appBundleIdentifier: String?
    public let isFullscreen: Bool
    public let zOrder: Int  // Lower values are closer to the front (0 is frontmost)

    public init(
        frame: CGRect, monitorID: UUID?, appBundleIdentifier: String?, isFullscreen: Bool,
        zOrder: Int = 0
    ) {
        self.frame = frame
        self.monitorID = monitorID
        self.appBundleIdentifier = appBundleIdentifier
        self.isFullscreen = isFullscreen
        self.zOrder = zOrder
    }
}

struct CustomLayout: Codable, Identifiable, Hashable, Equatable {
    var id = UUID()
    var name: String
    var layout: [CGRect]

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: CustomLayout, rhs: CustomLayout) -> Bool {
        return lhs.id == rhs.id
    }
}

// Structure to store display arrangement metadata
struct DisplayArrangementInfo: Codable, Equatable {
    let displayIDs: [CGDirectDisplayID]  // IDs of all displays in the arrangement
    let mainDisplayID: CGDirectDisplayID  // ID of the main display
    let displayFrames: [CGDirectDisplayID: CGRect]  // Frames of each display
    let createdAt: Date  // When this arrangement was captured

    static func == (lhs: DisplayArrangementInfo, rhs: DisplayArrangementInfo) -> Bool {
        // Check if the display IDs match
        guard lhs.displayIDs.count == rhs.displayIDs.count,
            Set(lhs.displayIDs) == Set(rhs.displayIDs),
            lhs.mainDisplayID == rhs.mainDisplayID
        else {
            return false
        }

        // Check if the display frames match
        for id in lhs.displayIDs {
            guard let lhsFrame = lhs.displayFrames[id],
                let rhsFrame = rhs.displayFrames[id],
                lhsFrame == rhsFrame
            else {
                return false
            }
        }

        return true
    }

    // Helper to determine if this arrangement is compatible with the current one
    func isCompatibleWith(currentArrangement: DisplayArrangementInfo) -> Bool {
        // Simple check: same number of displays and same main display
        return displayIDs.count == currentArrangement.displayIDs.count
            && mainDisplayID == currentArrangement.mainDisplayID
    }
}

struct Workspace: Codable, Identifiable, Hashable, Equatable {
    let id: UUID?  // Keep optional for now
    var name: String
    var windowInfos: [WindowInfo]
    var shortcutKeyCode: UInt16?
    var shortcutModifiers: UInt?  // Raw value of NSEvent.ModifierFlags

    // Display arrangement metadata
    var displayArrangement: DisplayArrangementInfo?

    var customLayout: CustomLayout?

    // Use global helper function for display
    var shortcutDisplayString: String? {
        // Just use the existing method for backward compatibility
        if let keyCode = shortcutKeyCode, let modifiers = shortcutModifiers {
            return formatShortcut(keyCode: keyCode, modifiers: modifiers)
        }
        return nil
    }

    static func == (lhs: Workspace, rhs: Workspace) -> Bool {
        return lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
