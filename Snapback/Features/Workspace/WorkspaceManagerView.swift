import Foundation
import KeyboardShortcuts
import SwiftUI

/// A view that displays a keyboard shortcut as a static tag
struct ShortcutTag: View {
    let shortcut: KeyboardShortcuts.Shortcut?

    var body: some View {
        if let shortcut = shortcut {
            Text(shortcut.description)
                .font(.system(.body, design: .monospaced))
                .tracking(1.2)  // Add letter spacing
                .padding(.horizontal, 8)  // Increased horizontal padding
                .padding(.vertical, 3)  // Increased vertical padding
                .background(SnapbackTheme.Background.card)
                .foregroundColor(SnapbackTheme.Text.primary)
                .cornerRadius(4)
        } else {
            Text("None")
                .font(.system(.body, design: .monospaced))
                .tracking(1.2)  // Add letter spacing
                .padding(.horizontal, 8)  // Increased horizontal padding
                .padding(.vertical, 3)  // Increased vertical padding
                .background(SnapbackTheme.Background.card)
                .foregroundColor(SnapbackTheme.Text.secondary)
                .cornerRadius(4)
        }
    }
}

struct WorkspaceManagerView: View {
    // Use the service's published property directly
    @EnvironmentObject var workspaceService: WorkspaceService

    // State for UI interaction
    @State private var selectedWorkspaceID: Workspace.ID?  // Select by ID
    @State private var showingDeleteAlert = false
    @State private var showProgress = false
    @State private var toastMessage: String?
    @State private var toastType: ToastType = .info

    // Reference to the AppDelegate to access the WindowManager
    private var appDelegate: AppDelegate? {
        NSApp.delegate as? AppDelegate
    }

    // Computed property to get the selected workspace object from the service
    private var selectedWorkspace: Workspace? {
        guard let selectedID = selectedWorkspaceID else { return nil }
        return workspaceService.workspaces.first { $0.id == selectedID }
    }

    enum ToastType { case success, error, info }

    // MARK: - Body
    var body: some View {
        NavigationStack {
            mainContentView
                .navigationTitle("Workspaces")
                .alert(
                    "Delete Workspace", isPresented: $showingDeleteAlert,
                    presenting: selectedWorkspaceID
                ) { id in
                    deleteAlertButtons(id: id)
                } message: { id in
                    deleteAlertMessage(id: id)
                }
        }
        .overlay(progressAndToastOverlay)
    }

    // MARK: - Computed View Properties
    @ViewBuilder
    private var mainContentView: some View {
        if workspaceService.workspaces.isEmpty {
            ContentUnavailableView(
                "No Workspaces Saved",
                systemImage: "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle",
                description: Text("Save your current window layout using Option+S or the menu bar.")
            )
        } else {
            workspaceListView
        }
    }

    private var workspaceListView: some View {
        VStack(spacing: 0) {
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(workspaceService.workspaces) { workspace in
                        WorkspaceCard(  // Pass data to the card
                            workspace: workspace,
                            onDelete: { deleteWorkspaceWithConfirmation(workspace: workspace) },
                            onEdit: {
                                print(
                                    "[WorkspaceManagerView] Edit button clicked for workspace: \(workspace.name)"
                                )
                                // Use the direct method to open the edit window
                                openEditWindow(for: workspace)
                            }
                        )
                        .environmentObject(workspaceService)
                    }
                }
                .padding()
            }

            // Import/Export buttons at the bottom using SettingsFooter for consistency
            SettingsFooter {
                HStack(spacing: SnapbackTheme.Padding.standard) {
                    Spacer()

                    Button("Import") {
                        importWorkspaces()
                    }
                    .keyboardShortcut(.cancelAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)

                    Button("Export") {
                        exportWorkspaces()
                    }
                    .keyboardShortcut(.defaultAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                }
            }
        }
    }

    @ViewBuilder
    private var progressAndToastOverlay: some View {
        Group {
            if showProgress {
                ProgressView().scaleEffect(1.5).padding().background(.ultraThinMaterial)
                    .cornerRadius(10)
            }
            if let message = toastMessage {
                VStack {
                    Spacer()
                    ToastView(message: message, type: toastType)
                }
                .padding(.bottom, 20).transition(.move(edge: .bottom)).animation(
                    .easeInOut, value: toastMessage)
            }
        }
    }

    // MARK: - Alert Helper Functions
    @ViewBuilder
    private func deleteAlertButtons(id: Workspace.ID?) -> some View {
        Button("Delete", role: .destructive) { confirmDelete(id: id) }
        Button("Cancel", role: .cancel) {}
    }

    private func deleteAlertMessage(id: Workspace.ID?) -> some View {
        let name = workspaceService.workspaces.first { $0.id == id }?.name ?? "this workspace"
        return Text("Are you sure you want to delete '\(name)'?")
    }

    // MARK: - Action Functions
    func exportWorkspaces() {
        showProgress = true
        let savePanel = NSSavePanel()
        savePanel.nameFieldStringValue = "Snapback_Workspaces.json"
        savePanel.allowedContentTypes = [.json]
        if savePanel.runModal() == .OK, let url = savePanel.url {
            do {
                let data = try JSONEncoder().encode(workspaceService.workspaces)
                try data.write(to: url)
                showToast(message: "Workspaces exported successfully!", type: .success)
            } catch {
                showToast(message: "Export failed: \(error.localizedDescription)", type: .error)
            }
        }
        showProgress = false
    }

    func importWorkspaces() {
        showProgress = true
        let openPanel = NSOpenPanel()
        openPanel.allowedContentTypes = [.json]
        openPanel.allowsMultipleSelection = false
        if openPanel.runModal() == .OK, let url = openPanel.url {
            do {
                let data = try Data(contentsOf: url)
                let importedWorkspaces = try JSONDecoder().decode([Workspace].self, from: data)
                for workspace in importedWorkspaces { workspaceService.addWorkspace(workspace) }
                showToast(
                    message: "\(importedWorkspaces.count) workspaces imported successfully!",
                    type: .success)
            } catch {
                showToast(message: "Import failed: \(error.localizedDescription)", type: .error)
            }
        }
        showProgress = false
    }

    private func showToast(message: String, type: ToastType) {
        toastMessage = message
        toastType = type
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { toastMessage = nil }
    }

    private func deleteWorkspaceWithConfirmation(workspace: Workspace) {
        selectedWorkspaceID = workspace.id
        showingDeleteAlert = true
    }

    func confirmDelete(id: Workspace.ID?) {
        guard let idToDelete = id else { return }
        workspaceService.deleteWorkspace(id: idToDelete)
        selectedWorkspaceID = nil
    }

    func deleteWorkspace(at offsets: IndexSet) {
        let idsToDelete = offsets.map { workspaceService.workspaces[$0].id }
        for id in idsToDelete { workspaceService.deleteWorkspace(id: id) }
        selectedWorkspaceID = nil
    }

    // Direct method to open edit window
    func openEditWindow(for workspace: Workspace) {
        print("[WorkspaceManagerView] openEditWindow called for workspace: \(workspace.name)")

        // Create the window directly
        let editWorkspaceView = EditWorkspaceView(workspace: workspace)
            .environmentObject(workspaceService)

        let editWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        let hostingView = NSHostingView(rootView: editWorkspaceView)
        editWorkspaceWindow.contentView = hostingView
        editWorkspaceWindow.title = "Edit Workspace"
        editWorkspaceWindow.isReleasedWhenClosed = true
        editWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            editWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .workspaceManager
        )

        let windowController = NSWindowController(window: editWorkspaceWindow)
        windowController.showWindow(nil)

        editWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        print("[WorkspaceManagerView] Edit window should now be visible")
    }
}

// Workspace card component
struct WorkspaceCard: View {
    let workspace: Workspace
    let onDelete: () -> Void
    var onEdit: (() -> Void)? = nil

    @EnvironmentObject var workspaceService: WorkspaceService
    @State private var isHovering = false

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                // Left side: Icon and name
                Image(systemName: "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle")
                    .foregroundColor(.accentColor).font(
                        .title2)
                // Keep name in a VStack in case we add subtitle later
                VStack(alignment: .leading) {
                    Text(workspace.name).font(.headline).foregroundColor(.primary)
                }

                Spacer(minLength: 50)  // Pushes controls to the far right with minimum spacing

                // Right side: Shortcut tag - fixed at the far right
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                    let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName)

                    ShortcutTag(shortcut: shortcut)
                        .frame(width: 120)
                        .padding(.trailing, 8)
                }

                // Menu button (3 vertical dots)
                Menu {
                    Button {
                        workspaceService.triggerRestoreWorkspace(workspace: workspace)
                    } label: {
                        Label("Restore Workspace", systemImage: "arrow.clockwise")
                    }

                    Divider()

                    if let editAction = onEdit {
                        Button(action: editAction) {
                            Label("Edit Workspace", systemImage: "pencil")
                        }
                    }

                    Button(action: onDelete) {
                        Label("Delete Workspace", systemImage: "trash")
                    }
                } label: {
                    ZStack {
                        Rectangle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 30, height: 30)
                            .cornerRadius(4)

                        Text("⋮")  // Vertical ellipsis character
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.secondary)
                    }
                }
                .menuStyle(.borderlessButton)
                .menuIndicator(.hidden)
                .fixedSize()  // This ensures the menu only activates when clicking on the icon
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(SnapbackTheme.Background.card)
            .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
            .overlay(
                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card).strokeBorder(
                    SnapbackTheme.Border.card,
                    lineWidth: SnapbackTheme.BorderWidth.normal))
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .contextMenu {
            Button {
                workspaceService.triggerRestoreWorkspace(workspace: workspace)
            } label: {
                Label("Restore Workspace", systemImage: "arrow.clockwise")
            }

            if let editAction = onEdit {
                Button(action: editAction) { Label("Edit Workspace", systemImage: "pencil") }
            }

            Button(action: onDelete) { Label("Delete Workspace", systemImage: "trash") }
        }
    }
}

// Toast message component
struct ToastView: View {
    let message: String
    let type: WorkspaceManagerView.ToastType
    var backgroundColor: Color {
        switch type {
        case .success: .green
        case .error: .red
        case .info: .blue
        }
    }
    var body: some View {
        Text(message).foregroundColor(.white).padding().background(backgroundColor).clipShape(
            RoundedRectangle(cornerRadius: 8)
        ).shadow(radius: 5)
    }
}

// Preview
#Preview {
    let snappingService = WindowSnappingService()
    let workspaceService = WorkspaceService(snappingService: snappingService)
    workspaceService.workspaces = [
        Workspace(
            id: UUID(), name: "Development Setup Long Name", windowInfos: [], shortcutKeyCode: 18,
            shortcutModifiers: NSEvent.ModifierFlags.command.rawValue),  // Cmd+1
        Workspace(
            id: UUID(), name: "Design", windowInfos: [], shortcutKeyCode: 8,
            shortcutModifiers: NSEvent.ModifierFlags.command.union(.shift).rawValue),  // Shift+Cmd+C
        Workspace(id: UUID(), name: "Comms", windowInfos: []),  // No shortcut
    ]
    return WorkspaceManagerView().environmentObject(workspaceService)
}
