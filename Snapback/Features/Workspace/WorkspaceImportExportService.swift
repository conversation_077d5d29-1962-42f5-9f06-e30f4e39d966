import Foundation
import AppKit
import SwiftUI

/// Service responsible for importing and exporting workspace configurations
class WorkspaceImportExportService {
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceImportExportService"
    
    // MARK: - Export Functionality
    
    /// Export workspaces to a JSON file
    /// - Parameters:
    ///   - workspaces: Array of workspaces to export
    ///   - completion: Completion handler with success/failure result
    func exportWorkspaces(_ workspaces: [Workspace], completion: @escaping (Result<URL, ExportError>) -> Void) {
        logger.info("Starting workspace export for \(workspaces.count) workspaces", service: serviceName, category: .workspaces)
        
        DispatchQueue.main.async {
            let savePanel = NSSavePanel()
            savePanel.title = "Export Workspaces"
            savePanel.nameFieldStringValue = "Snapback_Workspaces_\(self.dateString()).json"
            savePanel.allowedContentTypes = [.json]
            savePanel.canCreateDirectories = true
            
            let response = savePanel.runModal()
            
            if response == .OK, let url = savePanel.url {
                self.logger.debug("User selected export location: \(url.path)", service: self.serviceName, category: .workspaces)
                
                DispatchQueue.global(qos: .userInitiated).async {
                    do {
                        let exportData = WorkspaceExportData(
                            version: "1.0",
                            exportDate: Date(),
                            workspaces: workspaces
                        )
                        
                        let encoder = JSONEncoder()
                        encoder.dateEncodingStrategy = .iso8601
                        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
                        
                        let data = try encoder.encode(exportData)
                        try data.write(to: url)
                        
                        self.logger.info("Successfully exported \(workspaces.count) workspaces to \(url.path)", service: self.serviceName, category: .workspaces)
                        
                        DispatchQueue.main.async {
                            completion(.success(url))
                        }
                    } catch {
                        self.logger.error("Failed to export workspaces: \(error.localizedDescription)", service: self.serviceName, category: .workspaces)
                        
                        DispatchQueue.main.async {
                            completion(.failure(.encodingFailed(error)))
                        }
                    }
                }
            } else {
                self.logger.debug("User cancelled export", service: self.serviceName, category: .workspaces)
                DispatchQueue.main.async {
                    completion(.failure(.userCancelled))
                }
            }
        }
    }
    
    // MARK: - Import Functionality
    
    /// Import workspaces from a JSON file
    /// - Parameter completion: Completion handler with import result
    func importWorkspaces(completion: @escaping (Result<ImportResult, ImportError>) -> Void) {
        logger.info("Starting workspace import", service: serviceName, category: .workspaces)
        
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "Import Workspaces"
            openPanel.allowedContentTypes = [.json]
            openPanel.allowsMultipleSelection = false
            openPanel.canChooseDirectories = false
            openPanel.canChooseFiles = true
            
            let response = openPanel.runModal()
            
            if response == .OK, let url = openPanel.url {
                self.logger.debug("User selected import file: \(url.path)", service: self.serviceName, category: .workspaces)
                
                DispatchQueue.global(qos: .userInitiated).async {
                    do {
                        let data = try Data(contentsOf: url)
                        let importResult = try self.parseImportData(data)
                        
                        self.logger.info("Successfully parsed import file with \(importResult.workspaces.count) workspaces", service: self.serviceName, category: .workspaces)
                        
                        DispatchQueue.main.async {
                            completion(.success(importResult))
                        }
                    } catch let error as ImportError {
                        self.logger.error("Import failed: \(error.localizedDescription)", service: self.serviceName, category: .workspaces)
                        
                        DispatchQueue.main.async {
                            completion(.failure(error))
                        }
                    } catch {
                        self.logger.error("Import failed with unexpected error: \(error.localizedDescription)", service: self.serviceName, category: .workspaces)
                        
                        DispatchQueue.main.async {
                            completion(.failure(.decodingFailed(error)))
                        }
                    }
                }
            } else {
                self.logger.debug("User cancelled import", service: self.serviceName, category: .workspaces)
                DispatchQueue.main.async {
                    completion(.failure(.userCancelled))
                }
            }
        }
    }
    
    // MARK: - Conflict Resolution
    
    /// Check for conflicts between imported workspaces and existing ones
    /// - Parameters:
    ///   - importedWorkspaces: Workspaces to import
    ///   - existingWorkspaces: Currently existing workspaces
    /// - Returns: Array of conflicts found
    func checkForConflicts(importedWorkspaces: [Workspace], existingWorkspaces: [Workspace]) -> [WorkspaceConflict] {
        var conflicts: [WorkspaceConflict] = []
        
        for importedWorkspace in importedWorkspaces {
            // Check for name conflicts
            if let existingWorkspace = existingWorkspaces.first(where: { $0.name == importedWorkspace.name }) {
                conflicts.append(WorkspaceConflict(
                    importedWorkspace: importedWorkspace,
                    existingWorkspace: existingWorkspace,
                    conflictType: .nameConflict
                ))
            }
            
            // Check for shortcut conflicts
            if let importedKeyCode = importedWorkspace.shortcutKeyCode,
               let importedModifiers = importedWorkspace.shortcutModifiers {
                if let existingWorkspace = existingWorkspaces.first(where: { 
                    $0.shortcutKeyCode == importedKeyCode && $0.shortcutModifiers == importedModifiers 
                }) {
                    conflicts.append(WorkspaceConflict(
                        importedWorkspace: importedWorkspace,
                        existingWorkspace: existingWorkspace,
                        conflictType: .shortcutConflict
                    ))
                }
            }
        }
        
        logger.debug("Found \(conflicts.count) conflicts during import analysis", service: serviceName, category: .workspaces)
        return conflicts
    }
    
    // MARK: - Private Helper Methods
    
    private func parseImportData(_ data: Data) throws -> ImportResult {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        // Try to decode as new format first
        if let exportData = try? decoder.decode(WorkspaceExportData.self, from: data) {
            return ImportResult(
                workspaces: exportData.workspaces,
                version: exportData.version,
                exportDate: exportData.exportDate
            )
        }
        
        // Fallback to legacy format (direct array of workspaces)
        if let workspaces = try? decoder.decode([Workspace].self, from: data) {
            logger.debug("Imported file uses legacy format", service: serviceName, category: .workspaces)
            return ImportResult(
                workspaces: workspaces,
                version: "legacy",
                exportDate: nil
            )
        }
        
        throw ImportError.invalidFormat
    }
    
    private func dateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter.string(from: Date())
    }
}

// MARK: - Data Models

/// Container for exported workspace data with metadata
struct WorkspaceExportData: Codable {
    let version: String
    let exportDate: Date
    let workspaces: [Workspace]
}

/// Result of a successful import operation
struct ImportResult {
    let workspaces: [Workspace]
    let version: String
    let exportDate: Date?
}

/// Represents a conflict between imported and existing workspaces
struct WorkspaceConflict {
    let importedWorkspace: Workspace
    let existingWorkspace: Workspace
    let conflictType: ConflictType
    
    enum ConflictType {
        case nameConflict
        case shortcutConflict
    }
}

// MARK: - Error Types

enum ExportError: LocalizedError {
    case userCancelled
    case encodingFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .userCancelled:
            return "Export was cancelled by user"
        case .encodingFailed(let error):
            return "Failed to encode workspaces: \(error.localizedDescription)"
        }
    }
}

enum ImportError: LocalizedError {
    case userCancelled
    case fileNotFound
    case invalidFormat
    case decodingFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .userCancelled:
            return "Import was cancelled by user"
        case .fileNotFound:
            return "Selected file could not be found"
        case .invalidFormat:
            return "File format is not valid. Please select a valid Snapback workspace export file."
        case .decodingFailed(let error):
            return "Failed to read workspace data: \(error.localizedDescription)"
        }
    }
}
