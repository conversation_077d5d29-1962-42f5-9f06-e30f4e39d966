import Carbon
import Foundation
import KeyboardShortcuts
import MASShortcut
import SwiftUI

struct WorkspacesSettingsView: View {
    @EnvironmentObject var workspaceService: WorkspaceService
    @State private var viewWidth: CGFloat = 0
    @State private var refreshTrigger = UUID()

    // Determine if we should use two columns based on view width
    private var useTwoColumns: Bool {
        return viewWidth > 600
    }

    // Split workspaces into columns
    private var workspaceColumns: [[Workspace]] {
        let workspaces = workspaceService.workspaces

        if useTwoColumns && workspaces.count > 1 {
            let halfCount = (workspaces.count + 1) / 2
            let firstColumn = Array(workspaces.prefix(halfCount))
            let secondColumn = Array(workspaces.suffix(from: halfCount))
            return [firstColumn, secondColumn]
        } else {
            return [workspaces]
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                // Add a small spacer at the top to match other views
                Spacer().frame(height: 8)
                GeometryReader { geometry in
                    Color.clear.preference(key: WidthPreferenceKey.self, value: geometry.size.width)
                }
                .frame(height: 0)  // Make the GeometryReader invisible

                VStack(alignment: .leading, spacing: 16) {
                    // Workspace Shortcuts Section
                    Text("Workspace Shortcuts")
                        .snapbackSectionTitleStyle()

                    if !workspaceService.workspaces.isEmpty {
                        GroupBox {
                            VStack(spacing: 0) {
                                HStack(alignment: .top, spacing: 20) {
                                    // First column (or single column if not using two columns)
                                    VStack(spacing: 0) {
                                        ForEach(workspaceColumns[0].indices, id: \.self) { index in
                                            let workspace = workspaceColumns[0][index]
                                            WorkspaceSettingsRow(
                                                workspace: workspace,
                                                onSave: { updatedName in
                                                    var updatedWorkspace = workspace
                                                    updatedWorkspace.name = updatedName
                                                    workspaceService.updateWorkspace(
                                                        updatedWorkspace)
                                                },
                                                onDelete: { deleteWorkspace(workspace) }
                                            )
                                            .environmentObject(workspaceService)
                                            .padding(.vertical, 4)

                                            // Add divider after each row except the last one
                                            if index < workspaceColumns[0].count - 1 {
                                                Divider()
                                            }
                                        }
                                    }
                                    .frame(maxWidth: .infinity)

                                    // Second column (if using two columns)
                                    if useTwoColumns && workspaceColumns.count > 1 {
                                        VStack(spacing: 0) {
                                            ForEach(workspaceColumns[1].indices, id: \.self) {
                                                index in
                                                let workspace = workspaceColumns[1][index]
                                                WorkspaceSettingsRow(
                                                    workspace: workspace,
                                                    onSave: { updatedName in
                                                        var updatedWorkspace = workspace
                                                        updatedWorkspace.name = updatedName
                                                        workspaceService.updateWorkspace(
                                                            updatedWorkspace)
                                                    },
                                                    onDelete: { deleteWorkspace(workspace) }
                                                )
                                                .environmentObject(workspaceService)
                                                .padding(.vertical, 4)

                                                // Add divider after each row except the last one
                                                if index < workspaceColumns[1].count - 1 {
                                                    Divider()
                                                }
                                            }
                                        }
                                        .frame(maxWidth: .infinity)
                                    }
                                }
                            }
                        }

                        // Help text
                        Text(
                            "Click the shortcut field to record a new shortcut. Press esc to cancel."
                        )
                        .snapbackCaptionStyle()
                        .padding(.horizontal, 4)
                        .padding(.top, 4)
                    } else {
                        ContentUnavailableView(
                            "No Workspaces",
                            systemImage:
                                "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle",
                            description: Text(
                                "Save your current window layout using Option+S or the menu bar.")
                        )
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(SnapbackTheme.Background.card)
                        .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
                .id(refreshTrigger)  // Force refresh when needed
            }

            SettingsFooter(buttonTitle: "Reset All to Defaults") {
                resetAllWorkspaceShortcuts()
            }
        }
        .onPreferenceChange(WidthPreferenceKey.self) { width in
            viewWidth = width
        }
        .onAppear {
            // Migrate existing workspace shortcuts when the view appears
            migrateWorkspaceShortcuts()
        }

    }

    // Preference key for width
    private struct WidthPreferenceKey: PreferenceKey {
        static var defaultValue: CGFloat = 0
        static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
            value = nextValue()
        }
    }

    // Reset all workspace shortcuts
    private func resetAllWorkspaceShortcuts() {
        let logger = LoggingService.shared

        for workspace in workspaceService.workspaces {
            if let id = workspace.id {
                let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

                // Clear the shortcut by setting it to nil
                KeyboardShortcuts.setShortcut(nil, for: shortcutName)

                // Update the workspace in the service with nil values
                workspaceService.updateWorkspaceShortcut(id: id, keyCode: 0, modifiers: 0)

                logger.debug(
                    "Cleared shortcut for workspace: \(workspace.name)",
                    service: "WorkspacesSettingsView",
                    category: .shortcuts
                )
            }
        }

        // Force synchronize to ensure changes are written immediately
        UserDefaults.standard.synchronize()

        // Post notification to refresh menu
        NotificationCenter.default.post(name: Notification.Name("RefreshStatusMenu"), object: nil)

        // Force a refresh of the view
        refreshTrigger = UUID()
    }

    // Helper method to migrate existing workspace shortcuts to KeyboardShortcuts format
    private func migrateWorkspaceShortcuts() {
        let logger = LoggingService.shared

        for workspace in workspaceService.workspaces {
            if let id = workspace.id,
                let keyCode = workspace.shortcutKeyCode,
                let modifiers = workspace.shortcutModifiers
            {
                let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

                // Check if the shortcut is already set in KeyboardShortcuts
                if KeyboardShortcuts.getShortcut(for: shortcutName) == nil {
                    // If not set, create and set the shortcut
                    let key = KeyboardShortcuts.Key(rawValue: Int(keyCode))
                    let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)
                    let shortcut = KeyboardShortcuts.Shortcut(key, modifiers: modifierFlags)

                    // Set the shortcut in KeyboardShortcuts
                    KeyboardShortcuts.setShortcut(shortcut, for: shortcutName)

                    logger.debug(
                        "Migrated shortcut for workspace '\(workspace.name)'",
                        service: "WorkspacesSettingsView",
                        category: .shortcuts
                    )
                }
            }
        }
    }

    private func deleteWorkspace(_ workspace: Workspace) {
        if let id = workspace.id {
            workspaceService.deleteWorkspace(id: id)
        }
    }
}
