import SwiftUI

/// A reusable footer component for settings views
/// Provides consistent styling and layout for settings view footers
struct SettingsFooter: View {
    /// Background color for the footer (defaults to card background to match theme)
    var backgroundColor: Color = SnapbackTheme.Background.card

    /// Fixed height for the footer (defaults to 60)
    var height: CGFloat = 60

    /// Content to display in the footer
    let content: () -> AnyView

    /// Initialize with a single button
    /// - Parameters:
    ///   - buttonTitle: The title of the button
    ///   - height: Optional custom height for the footer
    ///   - action: The action to perform when the button is tapped
    init(buttonTitle: String, height: CGFloat = 60, action: @escaping () -> Void) {
        self.height = height
        self.content = {
            AnyView(
                HStack {
                    Spacer()
                    Button(buttonTitle) {
                        action()
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                }
            )
        }
    }

    /// Initialize with custom content
    /// - Parameters:
    ///   - height: Optional custom height for the footer
    ///   - content: A closure that returns the content to display in the footer
    init<Content: View>(height: CGFloat = 60, @ViewBuilder content: @escaping () -> Content) {
        self.height = height
        self.content = { AnyView(content()) }
    }

    var body: some View {
        VStack(spacing: 0) {
            Divider()

            content()
                .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                .frame(height: height)
                .frame(maxWidth: .infinity)
                .background(backgroundColor)
        }
    }
}

#Preview("Single Button Footer") {
    VStack {
        // Sample content
        ScrollView {
            VStack(alignment: .leading) {
                Text("Sample Content")
                    .font(.headline)
                    .padding()

                ForEach(1...5, id: \.self) { index in
                    Text("Item \(index)")
                        .padding()
                    Divider()
                }
            }
            .padding()
        }

        // Single button footer
        SettingsFooter(buttonTitle: "Reset All to Defaults") {
            print("Button tapped")
        }
    }
    .frame(width: 400, height: 400)
    .background(SnapbackTheme.Background.window)
}

#Preview("Multiple Buttons Footer") {
    VStack {
        // Sample content
        ScrollView {
            VStack(alignment: .leading) {
                Text("Sample Content")
                    .font(.headline)
                    .padding()
            }
            .padding()
        }

        // Multiple buttons footer
        SettingsFooter {
            HStack(spacing: 20) {
                // Left-aligned button
                Button("Update Positions") {
                    print("Update positions tapped")
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 16)

                Spacer()

                // Right-aligned buttons
                Button("Cancel") {
                    print("Cancel tapped")
                }
                .keyboardShortcut(.cancelAction)
                .padding(.vertical, 8)
                .padding(.horizontal, 16)

                Button("Save") {
                    print("Save tapped")
                }
                .keyboardShortcut(.defaultAction)
                .padding(.vertical, 8)
                .padding(.horizontal, 16)
            }
        }
    }
    .frame(width: 500, height: 300)
    .background(SnapbackTheme.Background.window)
}

#Preview("Height Comparison") {
    VStack(spacing: 0) {
        // Standard height footer
        SettingsFooter(
            buttonTitle: "Standard Height (60pt)",
            action: {
                print("Button tapped")
            })

        Divider()

        // Custom height footer
        SettingsFooter(
            buttonTitle: "Custom Height (50pt)",
            height: 50,
            action: {
                print("Button tapped")
            })

        Divider()

        // Another custom height footer
        SettingsFooter(
            buttonTitle: "Custom Height (70pt)",
            height: 70,
            action: {
                print("Button tapped")
            })
    }
    .frame(width: 500)
    .background(SnapbackTheme.Background.window)
}
