import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var licenseKeyInput: String = ""
    @State private var showingClearConfirmation = false

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image(systemName: statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // License Type
                                    HStack {
                                        Image(systemName: "doc.text")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("License Type")

                                        Spacer()

                                        Text(info.licenseType)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Registered User
                                    HStack {
                                        Image(systemName: "person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    Divider()

                                    // Expiration Date
                                    HStack {
                                        Image(systemName: "calendar")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Expires")

                                        Spacer()

                                        Text(info.expirationDisplayText)
                                            .foregroundColor(info.isExpired ? .red : .secondary)
                                    }
                                    .snapbackRowStyle()
                                }
                            }
                        }
                    }

                    // License Key Section
                    Text("License Key")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: SnapbackTheme.Padding.standard) {
                            // License Key Input
                            VStack(alignment: .leading, spacing: 6) {
                                HStack {
                                    Text("Enter License Key")
                                        .font(.system(size: SnapbackTheme.FontSize.body))

                                    Spacer()

                                    if licenseManager.isValidating {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    }
                                }

                                TextField("XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput)
                                    .textFieldStyle(.roundedBorder)
                                    .font(.system(.body, design: .monospaced))
                                    .disabled(licenseManager.isValidating)
                                    .autocorrectionDisabled()
                                    .textInputAutocapitalization(.characters)
                                    .onSubmit {
                                        Task {
                                            await licenseManager.setLicenseKey(licenseKeyInput)
                                        }
                                    }
                                    .onChange(of: licenseKeyInput) { oldValue, newValue in
                                        // Auto-format license key with dashes
                                        let cleaned = newValue.replacingOccurrences(
                                            of: "-", with: ""
                                        ).uppercased()
                                        if cleaned.count <= 16 {
                                            let formatted = formatLicenseKey(cleaned)
                                            if formatted != newValue {
                                                licenseKeyInput = formatted
                                            }
                                        } else {
                                            licenseKeyInput = oldValue
                                        }
                                    }

                                // Error Message
                                if let error = licenseManager.lastError {
                                    Text(error)
                                        .snapbackErrorStyle()
                                        .font(.system(size: SnapbackTheme.FontSize.caption))
                                }
                            }
                            .padding(.vertical, SnapbackTheme.Padding.small)

                            // Action Buttons
                            HStack(spacing: SnapbackTheme.Padding.standard) {
                                // Validate Button
                                Button("Validate License") {
                                    Task {
                                        await licenseManager.setLicenseKey(licenseKeyInput)
                                    }
                                }
                                .disabled(licenseKeyInput.isEmpty || licenseManager.isValidating)

                                Spacer()

                                // Clear Button (only show if there's a license)
                                if !licenseManager.licenseKey.isEmpty {
                                    Button("Clear License") {
                                        showingClearConfirmation = true
                                    }
                                    .foregroundColor(.red)
                                }
                            }
                        }
                        .padding(.vertical, SnapbackTheme.Padding.small)
                    }

                    // Features Section (if license is valid)
                    if let info = licenseManager.licenseInfo, licenseManager.licenseStatus == .valid
                    {
                        Text("Licensed Features")
                            .snapbackSectionTitleStyle()

                        GroupBox {
                            VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                                ForEach(info.features, id: \.self) { feature in
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                            .frame(width: 16)

                                        Text(feature)
                                            .font(.system(size: SnapbackTheme.FontSize.body))

                                        Spacer()
                                    }
                                    .padding(.vertical, 2)
                                }
                            }
                            .padding(.vertical, SnapbackTheme.Padding.small)
                        }
                    }

                    // Help Section
                    Text("Help")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                            Text("Need help with licensing?")
                                .font(.system(size: SnapbackTheme.FontSize.body, weight: .medium))

                            Text(
                                "• Contact support for license issues\n• Visit our website for purchasing options\n• Check your email for license key delivery"
                            )
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)

                            HStack {
                                Button("Contact Support") {
                                    if let url = URL(string: "mailto:<EMAIL>") {
                                        NSWorkspace.shared.open(url)
                                    }
                                }
                                .font(.system(size: SnapbackTheme.FontSize.caption))

                                Spacer()

                                Button("Purchase License") {
                                    if let url = URL(string: "https://snapback.app/purchase") {
                                        NSWorkspace.shared.open(url)
                                    }
                                }
                                .font(.system(size: SnapbackTheme.FontSize.caption))
                            }
                        }
                        .padding(.vertical, SnapbackTheme.Padding.small)
                    }

                    Spacer(minLength: 20)
                }
                .padding(SnapbackTheme.Padding.section)
            }

            // Footer with Reset Button
            SettingsFooter(buttonTitle: "Reset License Settings") {
                showingClearConfirmation = true
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Load current license key into input field
            licenseKeyInput = licenseManager.licenseKey
        }
        .alert("Clear License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Clear", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to clear the current license? This action cannot be undone.")
        }
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .unlicensed:
            return "exclamationmark.triangle"
        case .valid:
            return "checkmark.circle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .expired:
            return "clock.badge.exclamationmark"
        }
    }

    // MARK: - Helper Methods

    /// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
    private func formatLicenseKey(_ key: String) -> String {
        let cleaned = key.replacingOccurrences(of: "-", with: "")
        var formatted = ""

        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }
}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
