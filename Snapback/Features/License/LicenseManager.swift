import Foundation
import SwiftUI

/// Manages license validation and storage for the Snapback app
class LicenseManager: ObservableObject {
    static let shared = LicenseManager()

    /// Published properties for UI binding
    @Published var licenseStatus: LicenseStatus = .unlicensed
    @Published var licenseKey: String = ""
    @Published var licenseInfo: LicenseInfo?
    @Published var isValidating: Bool = false
    @Published var lastError: String?

    /// Logger for license-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseManager"

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let licenseKey = "SnapbackLicenseKey"
        static let licenseStatus = "SnapbackLicenseStatus"
        static let licenseInfo = "SnapbackLicenseInfo"
        static let lastValidation = "SnapbackLastLicenseValidation"
    }

    /// License validation endpoint (placeholder - replace with actual endpoint)
    private let validationEndpoint = "https://api.snapback.app/license/validate"

    private init() {
        logger.info("Initializing LicenseManager", service: serviceName)
        loadPersistedLicense()

        // Validate license on startup if we have one
        if !licenseKey.isEmpty {
            Task {
                await validateLicense(silent: true)
            }
        }
    }

    // MARK: - Public Methods

    /// Validate the current license key
    @MainActor
    func validateLicense(silent: Bool = false) async {
        guard !licenseKey.isEmpty else {
            if !silent {
                lastError = "Please enter a license key"
            }
            return
        }

        isValidating = true
        lastError = nil

        logger.info("Validating license key: \(licenseKey.prefix(8))...", service: serviceName)

        do {
            let result = try await performLicenseValidation(licenseKey)

            switch result.status {
            case .valid:
                licenseStatus = .valid
                licenseInfo = result.info
                logger.info("License validation successful", service: serviceName)

            case .invalid:
                licenseStatus = .invalid
                licenseInfo = nil
                lastError = result.message ?? "Invalid license key"
                logger.warning("License validation failed: invalid key", service: serviceName)

            case .expired:
                licenseStatus = .expired
                licenseInfo = result.info
                lastError = result.message ?? "License has expired"
                logger.warning("License validation failed: expired", service: serviceName)

            case .unlicensed:
                licenseStatus = .unlicensed
                licenseInfo = nil
                lastError = result.message ?? "No license found"
                logger.info("No license found", service: serviceName)
            }

            // Persist the results
            persistLicense()

        } catch {
            licenseStatus = .invalid
            licenseInfo = nil
            lastError = "Validation failed: \(error.localizedDescription)"
            logger.error("License validation error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Set a new license key and validate it
    @MainActor
    func setLicenseKey(_ key: String) async {
        let cleanKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        licenseKey = cleanKey

        if !cleanKey.isEmpty {
            await validateLicense()
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Clear the current license
    func clearLicense() {
        logger.info("Clearing license", service: serviceName)

        licenseKey = ""
        licenseStatus = .unlicensed
        licenseInfo = nil
        lastError = nil

        // Clear from UserDefaults
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastValidation)
        UserDefaults.standard.synchronize()
    }

    // MARK: - Private Methods

    /// Load persisted license information
    private func loadPersistedLicense() {
        licenseKey = UserDefaults.standard.string(forKey: PersistenceKeys.licenseKey) ?? ""

        if let statusRaw = UserDefaults.standard.string(forKey: PersistenceKeys.licenseStatus),
            let status = LicenseStatus(rawValue: statusRaw)
        {
            licenseStatus = status
        }

        if let infoData = UserDefaults.standard.data(forKey: PersistenceKeys.licenseInfo),
            let info = try? JSONDecoder().decode(LicenseInfo.self, from: infoData)
        {
            licenseInfo = info
        }

        logger.info("Loaded persisted license - Status: \(licenseStatus)", service: serviceName)
    }

    /// Persist license information to UserDefaults
    private func persistLicense() {
        UserDefaults.standard.set(licenseKey, forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.set(licenseStatus.rawValue, forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastValidation)

        if let info = licenseInfo,
            let infoData = try? JSONEncoder().encode(info)
        {
            UserDefaults.standard.set(infoData, forKey: PersistenceKeys.licenseInfo)
        } else {
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        }

        UserDefaults.standard.synchronize()
        logger.debug("License information persisted", service: serviceName)
    }

    /// Perform the actual license validation (placeholder implementation)
    private func performLicenseValidation(_ key: String) async throws -> LicenseValidationResult {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000)  // 1 second

        // Clean the key for comparison
        let cleanKey = key.replacingOccurrences(of: "-", with: "").lowercased()

        // Demo license keys for testing
        switch cleanKey {
        case "demo1234demo1234":
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Demo License",
                    registeredUser: "Demo User",
                    expirationDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()),
                    features: ["Basic Window Management", "5 Workspaces", "Standard Support"]
                ),
                message: "Demo license activated successfully"
            )

        case "pro1234567890abcd":
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Pro License",
                    registeredUser: "Professional User",
                    expirationDate: nil,  // Lifetime license
                    features: [
                        "All Window Management Features", "Unlimited Workspaces",
                        "Advanced Shortcuts", "Priority Support", "CloudKit Sync",
                    ]
                ),
                message: "Pro license activated successfully"
            )

        case "expired123456789":
            return LicenseValidationResult(
                status: .expired,
                info: LicenseInfo(
                    licenseType: "Standard License",
                    registeredUser: "Test User",
                    expirationDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),
                    features: ["All Features"]
                ),
                message: "License has expired. Please renew your license."
            )

        case "trial12345678901":
            return LicenseValidationResult(
                status: .valid,
                info: LicenseInfo(
                    licenseType: "Trial License",
                    registeredUser: "Trial User",
                    expirationDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()),
                    features: ["All Features", "7-Day Trial"]
                ),
                message: "Trial license activated"
            )

        default:
            // Check for common patterns
            if cleanKey.hasPrefix("demo") {
                return LicenseValidationResult(
                    status: .valid,
                    info: LicenseInfo(
                        licenseType: "Demo License",
                        registeredUser: "Demo User",
                        expirationDate: Calendar.current.date(
                            byAdding: .day, value: 30, to: Date()),
                        features: ["Basic Features", "Limited Workspaces"]
                    ),
                    message: "Demo license activated"
                )
            } else if cleanKey.hasPrefix("pro") {
                return LicenseValidationResult(
                    status: .valid,
                    info: LicenseInfo(
                        licenseType: "Pro License",
                        registeredUser: "Pro User",
                        expirationDate: nil,
                        features: ["All Features", "Unlimited Workspaces", "Priority Support"]
                    ),
                    message: "Pro license activated"
                )
            } else {
                return LicenseValidationResult(
                    status: .invalid,
                    info: nil,
                    message: "Invalid license key. Please check your key and try again."
                )
            }
        }
    }
}

// MARK: - Supporting Types

/// License status enumeration
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .unlicensed: return "No License"
        case .valid: return "Valid"
        case .invalid: return "Invalid"
        case .expired: return "Expired"
        }
    }

    var color: Color {
        switch self {
        case .unlicensed: return .secondary
        case .valid: return .green
        case .invalid: return .red
        case .expired: return .orange
        }
    }
}

/// License information structure
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let expirationDate: Date?
    let features: [String]

    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return expirationDate < Date()
    }

    var expirationDisplayText: String {
        guard let expirationDate = expirationDate else { return "Never expires" }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return formatter.string(from: expirationDate)
    }
}

/// License validation result
struct LicenseValidationResult {
    let status: LicenseStatus
    let info: LicenseInfo?
    let message: String?
}
