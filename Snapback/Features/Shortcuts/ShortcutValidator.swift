import Foundation
import KeyboardShortcuts
import MASShortcut

/// Protocol for shortcut validation
protocol ShortcutValidatorProtocol {
    /// Check if a shortcut is valid (not conflicting with system or other shortcuts)
    func isShortcutValid(_ shortcut: KeyboardShortcuts.Shortcut) -> Bool

    /// Check if a shortcut conflicts with system shortcuts
    func isShortcutConflictingWithSystem(_ shortcut: KeyboardShortcuts.Shortcut) -> (Bool, String?)

    /// Check if a shortcut conflicts with other applications
    func isShortcutConflictingWithOtherApps(_ shortcut: KeyboardShortcuts.Shortcut) -> (
        Bool, String?
    )
}

/// Standard shortcut validator that checks for conflicts
class StandardShortcutValidator: ShortcutValidatorProtocol {
    static let shared = StandardShortcutValidator()

    private let systemShortcutChecker = SystemShortcutChecker.shared
    private let masValidator: MASShortcutValidator! = MASShortcutValidator.shared()

    func isShortcutValid(_ shortcut: KeyboardShortcuts.Shortcut) -> Bool {
        guard let key = shortcut.key else { return false }

        let keyCode = UInt16(key.rawValue)
        let modifiers = shortcut.modifiers.rawValue

        // Convert to MASShortcut for validation
        let masShortcut = MASShortcut(
            keyCode: Int(keyCode), modifierFlags: NSEvent.ModifierFlags(rawValue: modifiers))

        // Check if the shortcut is valid according to MASShortcut
        return masValidator.isShortcutValid(masShortcut)
    }

    func isShortcutConflictingWithSystem(_ shortcut: KeyboardShortcuts.Shortcut) -> (Bool, String?)
    {
        guard let key = shortcut.key else { return (false, nil) }

        let keyCode = UInt16(key.rawValue)
        let modifiers = shortcut.modifiers.rawValue

        // Convert to MASShortcut for validation
        let masShortcut = MASShortcut(
            keyCode: Int(keyCode), modifierFlags: NSEvent.ModifierFlags(rawValue: modifiers))

        // Check for system conflicts
        var explanation: NSString?
        let isConflicting = masValidator.isShortcutAlreadyTaken(
            bySystem: masShortcut, explanation: &explanation)

        // If MASShortcut found a conflict, return it
        if isConflicting {
            return (isConflicting, explanation as String?)
        }

        // Otherwise, check our custom system shortcut checker
        if let conflict = systemShortcutChecker.checkForSystemConflict(
            keyCode: keyCode, modifiers: modifiers)
        {
            return (true, conflict)
        }

        return (false, nil)
    }

    func isShortcutConflictingWithOtherApps(_ shortcut: KeyboardShortcuts.Shortcut) -> (
        Bool, String?
    ) {
        guard let key = shortcut.key else { return (false, nil) }

        let keyCode = UInt16(key.rawValue)
        let modifiers = shortcut.modifiers.rawValue

        // Convert to MASShortcut for validation
        let masShortcut = MASShortcut(
            keyCode: Int(keyCode), modifierFlags: NSEvent.ModifierFlags(rawValue: modifiers))

        // Check for conflicts with other applications
        var explanation: NSString?
        let isConflicting = masValidator.isShortcut(
            masShortcut, alreadyTakenIn: nil, explanation: &explanation)

        return (isConflicting, explanation as String?)
    }
}

/// Passthrough shortcut validator that bypasses all conflict checks
class PassthroughShortcutValidator: ShortcutValidatorProtocol {
    static let shared = PassthroughShortcutValidator()

    func isShortcutValid(_ shortcut: KeyboardShortcuts.Shortcut) -> Bool {
        // Always return true, allowing any shortcut
        return true
    }

    func isShortcutConflictingWithSystem(_ shortcut: KeyboardShortcuts.Shortcut) -> (Bool, String?)
    {
        // Always return false, ignoring system shortcut conflicts
        return (false, nil)
    }

    func isShortcutConflictingWithOtherApps(_ shortcut: KeyboardShortcuts.Shortcut) -> (
        Bool, String?
    ) {
        // Always return false, ignoring conflicts with other applications
        return (false, nil)
    }
}

/// Manager class for shortcut validation
class ShortcutValidatorManager {
    static let shared = ShortcutValidatorManager()

    private(set) var currentValidator: ShortcutValidatorProtocol

    private init() {
        // Initialize with the appropriate validator based on user preferences
        let allowAnyShortcut = DefaultsManager.shared.allowAnyShortcut
        currentValidator =
            allowAnyShortcut
            ? PassthroughShortcutValidator.shared : StandardShortcutValidator.shared
    }

    /// Set whether to allow any shortcut (bypass conflict checking)
    func setAllowAnyShortcut(_ allow: Bool) {
        // Update the validator
        currentValidator =
            allow ? PassthroughShortcutValidator.shared : StandardShortcutValidator.shared

        // Post notification for observers
        NotificationCenter.default.post(
            name: Notification.Name("ShortcutValidatorChanged"),
            object: allow
        )
    }

    /// Get whether any shortcut is allowed (conflict checking is bypassed)
    var allowAnyShortcut: Bool {
        return DefaultsManager.shared.allowAnyShortcut
    }
}
