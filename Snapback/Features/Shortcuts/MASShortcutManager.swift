import AppKit
import Carbon.HIToolbox
import Foundation
import MASShortcut

/// Implementation of ShortcutManagerProtocol using MASShortcut
public class MASShortcutManager: ShortcutManagerProtocol {
    /// The prefix for user defaults keys
    private let userDefaultsPrefix = "Snapback.Shortcuts."

    /// Logger
    private let logger = LoggingService.shared
    private let serviceName = "MASShortcutManager"

    /// The registered shortcuts
    private var registeredShortcuts: [String: MASShortcut] = [:]

    /// The default shortcuts
    private var defaultShortcuts: [String: Shortcut] = [:]

    /// The MASShortcutValidator instance
    private var validator: MASShortcutValidator? {
        return MASShortcutValidator.shared()
    }

    /// Initialize a new MASShortcutManager
    public init() {
        // Set up default shortcuts if needed
        setupDefaultShortcuts()

        logger.debug("MASShortcutManager initialized", service: serviceName, category: .shortcuts)
    }

    /// Set up default shortcuts
    private func setupDefaultShortcuts() {
        // You can add default shortcuts here
        // For example:
        defaultShortcuts["leftHalf"] = Shortcut(
            keyCode: Int(kVK_LeftArrow),
            modifiers: [.control, .option]
        )

        defaultShortcuts["rightHalf"] = Shortcut(
            keyCode: Int(kVK_RightArrow),
            modifiers: [.control, .option]
        )

        // Add more default shortcuts as needed
    }

    /// Convert a Shortcut to a MASShortcut
    /// - Parameter shortcut: The shortcut to convert
    /// - Returns: The MASShortcut
    private func toMASShortcut(_ shortcut: Shortcut) -> MASShortcut {
        return MASShortcut(keyCode: shortcut.keyCode, modifierFlags: shortcut.modifiers)
    }

    /// Convert a MASShortcut to a Shortcut
    /// - Parameter masShortcut: The MASShortcut to convert
    /// - Returns: The Shortcut
    private func fromMASShortcut(_ masShortcut: MASShortcut) -> Shortcut {
        return Shortcut(keyCode: Int(masShortcut.keyCode), modifiers: masShortcut.modifierFlags)
    }

    /// Get the user defaults key for a shortcut name
    /// - Parameter name: The shortcut name
    /// - Returns: The user defaults key
    private func userDefaultsKey(for name: String) -> String {
        return "\(userDefaultsPrefix)\(name)"
    }

    // MARK: - ShortcutManagerProtocol Implementation

    public func registerShortcut(
        _ shortcut: Shortcut, forName name: String, action: @escaping () -> Void
    ) {
        // Note: MASShortcut registration has been removed
        // We only store the shortcut in UserDefaults for persistence

        let masShortcut = toMASShortcut(shortcut)

        logger.debug(
            "Registering shortcut for '\(name)' - Key code: \(shortcut.keyCode), Modifiers: \(shortcut.modifiers)",
            service: serviceName,
            category: .shortcuts
        )

        // Save the shortcut to user defaults
        if let data = try? NSKeyedArchiver.archivedData(
            withRootObject: masShortcut, requiringSecureCoding: false)
        {
            UserDefaults.standard.set(data, forKey: userDefaultsKey(for: name))
        } else {
            logger.error(
                "Failed to archive shortcut data for '\(name)'",
                service: serviceName,
                category: .shortcuts
            )
        }

        // Post a notification that the shortcut changed
        NotificationCenter.default.post(
            name: Notification.Name("ShortcutManager.shortcutChanged"),
            object: nil,
            userInfo: ["name": name]
        )
    }

    public func unregisterShortcut(forName name: String) {
        // Note: MASShortcut unregistration has been removed
        // We only remove the shortcut from our internal tracking

        logger.debug(
            "Unregistering shortcut for '\(name)'",
            service: serviceName,
            category: .shortcuts
        )

        // Remove the shortcut from the registered shortcuts
        registeredShortcuts.removeValue(forKey: name)

        // Remove from UserDefaults if needed
        // UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
    }

    public func unregisterAllShortcuts() {
        // Note: MASShortcut unregistration has been removed
        // We only clear our internal tracking

        logger.debug(
            "Unregistering all shortcuts",
            service: serviceName,
            category: .shortcuts
        )

        // Clear the registered shortcuts
        registeredShortcuts.removeAll()

        // Could also clear from UserDefaults if needed
        // for (name, _) in registeredShortcuts {
        //     UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
        // }
    }

    public func getShortcut(forName name: String) -> Shortcut? {
        // Try to get the shortcut from user defaults
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey(for: name)),
            let masShortcut = try? NSKeyedUnarchiver.unarchivedObject(
                ofClass: MASShortcut.self, from: data)
        {
            return fromMASShortcut(masShortcut)
        }

        // Fall back to default shortcut if available
        return defaultShortcuts[name]
    }

    public func setShortcut(_ shortcut: Shortcut?, forName name: String) {
        // Unregister any existing shortcut with the same name
        unregisterShortcut(forName: name)

        if let shortcut = shortcut {
            // Convert to MASShortcut
            let masShortcut = toMASShortcut(shortcut)

            logger.debug(
                "Setting shortcut for '\(name)' - Key code: \(shortcut.keyCode), Modifiers: \(shortcut.modifiers)",
                service: serviceName,
                category: .shortcuts
            )

            // Save the shortcut to user defaults
            if let data = try? NSKeyedArchiver.archivedData(
                withRootObject: masShortcut, requiringSecureCoding: false)
            {
                UserDefaults.standard.set(data, forKey: userDefaultsKey(for: name))
            } else {
                logger.error(
                    "Failed to archive shortcut data for '\(name)'",
                    service: serviceName,
                    category: .shortcuts
                )
            }
        } else {
            logger.debug(
                "Removing shortcut for '\(name)'",
                service: serviceName,
                category: .shortcuts
            )

            // Remove the shortcut from user defaults
            UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
        }

        // Post a notification that the shortcut changed
        NotificationCenter.default.post(
            name: Notification.Name("ShortcutManager.shortcutChanged"),
            object: nil,
            userInfo: ["name": name]
        )
    }

    public func resetShortcut(forName name: String) {
        // Get the default shortcut
        if let defaultShortcut = defaultShortcuts[name] {
            logger.debug(
                "Resetting shortcut for '\(name)' to default - Key code: \(defaultShortcut.keyCode), Modifiers: \(defaultShortcut.modifiers)",
                service: serviceName,
                category: .shortcuts
            )

            // Set the shortcut to the default
            setShortcut(defaultShortcut, forName: name)
        } else {
            logger.debug(
                "No default shortcut for '\(name)', removing shortcut",
                service: serviceName,
                category: .shortcuts
            )

            // Remove the shortcut if there's no default
            setShortcut(nil, forName: name)
        }
    }

    public func resetAllShortcuts() {
        logger.debug(
            "Resetting all shortcuts to defaults",
            service: serviceName,
            category: .shortcuts
        )

        // Reset all shortcuts to their defaults
        for (name, _) in defaultShortcuts {
            resetShortcut(forName: name)
        }
    }

    public func validateShortcut(_ shortcut: Shortcut) -> ShortcutValidationResult {
        // If "Allow any keyboard shortcut" is enabled, bypass validation
        if DefaultsManager.shared.allowAnyShortcut {
            logger.debug(
                "Shortcut validation bypassed due to 'Allow any keyboard shortcut' setting",
                service: serviceName,
                category: .shortcuts
            )
            return ShortcutValidationResult(isValid: true)
        }

        let masShortcut = toMASShortcut(shortcut)
        guard let validator = MASShortcutValidator.shared() else {
            logger.warning(
                "MASShortcutValidator not available, validation bypassed",
                service: serviceName,
                category: .shortcuts
            )
            return ShortcutValidationResult(isValid: true)
        }

        // Check if the shortcut is valid (has at least one modifier and a valid key code)
        if !validator.isShortcutValid(masShortcut) {
            // Determine the specific reason for invalidity
            var reason = "The shortcut is not valid"

            if masShortcut.modifierFlags.isEmpty {
                reason =
                    "Shortcut must include at least one modifier key (Command, Option, Control, or Shift)"
            } else if masShortcut.keyCode == 0xFF {
                reason = "Invalid key code"
            } else if masShortcut.modifierFlags.rawValue == NSEvent.ModifierFlags.shift.rawValue
                && (masShortcut.keyCode == kVK_Tab || masShortcut.keyCode == kVK_Return
                    || masShortcut.keyCode == kVK_Space || masShortcut.keyCode == kVK_Delete)
            {
                reason = "Shift + Tab/Return/Space/Delete are reserved system shortcuts"
            }

            logger.debug(
                "Shortcut validation failed: \(reason) - Key code: \(masShortcut.keyCode), Modifiers: \(masShortcut.modifierFlags)",
                service: serviceName,
                category: .shortcuts
            )

            return ShortcutValidationResult(
                isValid: false,
                invalidReason: reason
            )
        }

        // Check for system conflicts
        var systemExplanation: NSString?
        let hasSystemConflict = validator.isShortcutAlreadyTaken(
            bySystem: masShortcut,
            explanation: &systemExplanation
        )

        if hasSystemConflict {
            let conflictReason = systemExplanation as String? ?? "Unknown system shortcut"
            logger.debug(
                "Shortcut validation failed: Conflicts with system shortcut - \(conflictReason)",
                service: serviceName,
                category: .shortcuts
            )

            return ShortcutValidationResult(
                isValid: false,
                invalidReason: "Conflicts with a system shortcut",
                conflictingApplication: conflictReason
            )
        }

        // Check for application conflicts
        var appExplanation: NSString?
        let hasAppConflict = validator.isShortcut(
            masShortcut,
            alreadyTakenIn: nil,
            explanation: &appExplanation
        )

        if hasAppConflict {
            let conflictReason = appExplanation as String? ?? "Unknown application"
            logger.debug(
                "Shortcut validation failed: Conflicts with application shortcut - \(conflictReason)",
                service: serviceName,
                category: .shortcuts
            )

            return ShortcutValidationResult(
                isValid: false,
                invalidReason: "Conflicts with another application",
                conflictingApplication: conflictReason
            )
        }

        // Check for conflicts with other window management applications
        let (hasWindowManagerConflict, conflictingAppNames) = ConflictingAppsChecker.shared
            .checkShortcutForConflicts(keyCode: shortcut.keyCode, modifiers: shortcut.modifiers)

        if hasWindowManagerConflict, let appNames = conflictingAppNames {
            logger.debug(
                "Shortcut validation failed: Conflicts with window manager - \(appNames)",
                service: serviceName,
                category: .shortcuts
            )

            return ShortcutValidationResult(
                isValid: false,
                invalidReason: "Conflicts with another window manager",
                conflictingApplication: appNames
            )
        }

        // The shortcut is valid
        logger.debug(
            "Shortcut validation successful for key code: \(masShortcut.keyCode), modifiers: \(masShortcut.modifierFlags)",
            service: serviceName,
            category: .shortcuts
        )

        return ShortcutValidationResult(isValid: true)
    }

    public func checkSystemConflicts(for shortcut: Shortcut) -> (Bool, String?) {
        // If "Allow any keyboard shortcut" is enabled, bypass validation
        if DefaultsManager.shared.allowAnyShortcut {
            logger.debug(
                "System conflict check bypassed due to 'Allow any keyboard shortcut' setting",
                service: serviceName,
                category: .shortcuts
            )
            return (false, nil)
        }

        let masShortcut = toMASShortcut(shortcut)
        guard let validator = MASShortcutValidator.shared() else {
            logger.warning(
                "MASShortcutValidator not available, system conflict check bypassed",
                service: serviceName,
                category: .shortcuts
            )
            return (false, nil)
        }

        var explanation: NSString?
        let hasConflict = validator.isShortcutAlreadyTaken(
            bySystem: masShortcut,
            explanation: &explanation
        )

        if hasConflict {
            let conflictReason = explanation as String? ?? "Unknown system shortcut"
            logger.debug(
                "System conflict detected: \(conflictReason) for key code: \(masShortcut.keyCode), modifiers: \(masShortcut.modifierFlags)",
                service: serviceName,
                category: .shortcuts
            )
        }

        return (hasConflict, explanation as String?)
    }

    public func checkApplicationConflicts(for shortcut: Shortcut) -> (Bool, String?) {
        // If "Allow any keyboard shortcut" is enabled, bypass validation
        if DefaultsManager.shared.allowAnyShortcut {
            logger.debug(
                "Application conflict check bypassed due to 'Allow any keyboard shortcut' setting",
                service: serviceName,
                category: .shortcuts
            )
            return (false, nil)
        }

        let masShortcut = toMASShortcut(shortcut)
        guard let validator = MASShortcutValidator.shared() else {
            logger.warning(
                "MASShortcutValidator not available, application conflict check bypassed",
                service: serviceName,
                category: .shortcuts
            )
            return (false, nil)
        }

        var explanation: NSString?
        let hasConflict = validator.isShortcut(
            masShortcut,
            alreadyTakenIn: nil,
            explanation: &explanation
        )

        if hasConflict {
            let conflictReason = explanation as String? ?? "Unknown application"
            logger.debug(
                "Application conflict detected: \(conflictReason) for key code: \(masShortcut.keyCode), modifiers: \(masShortcut.modifierFlags)",
                service: serviceName,
                category: .shortcuts
            )
            return (hasConflict, explanation as String?)
        }

        // Also check for conflicts with other window management applications
        let (hasWindowManagerConflict, conflictingAppNames) = ConflictingAppsChecker.shared
            .checkShortcutForConflicts(keyCode: shortcut.keyCode, modifiers: shortcut.modifiers)

        if hasWindowManagerConflict, let appNames = conflictingAppNames {
            logger.debug(
                "Window manager conflict detected: \(appNames) for key code: \(shortcut.keyCode), modifiers: \(shortcut.modifiers)",
                service: serviceName,
                category: .shortcuts
            )
            return (true, "Window manager: \(appNames)")
        }

        return (false, nil)
    }
}
