import AppKit
import Carbon
import Foundation
import MASShortcut

class SystemShortcutChecker {
    static let shared = SystemShortcutChecker()

    /// Logger
    private let logger = LoggingService.shared
    private let serviceName = "SystemShortcutChecker"

    private init() {
        logger.debug(
            "SystemShortcutChecker initialized", service: serviceName, category: .shortcuts)
    }

    // Common system shortcuts to check against
    private let knownSystemShortcuts: [(keyCode: UInt16, modifiers: UInt, description: String)] = [
        // Mission Control
        (UInt16(kVK_UpArrow), NSEvent.ModifierFlags.control.rawValue, "Mission Control"),
        (UInt16(kVK_DownArrow), NSEvent.ModifierFlags.control.rawValue, "Application Windows"),
        (UInt16(kVK_LeftArrow), NSEvent.ModifierFlags.control.rawValue, "Move left a space"),
        (UInt16(kVK_RightArrow), NSEvent.ModifierFlags.control.rawValue, "Move right a space"),

        // Screenshots
        (
            UInt16(kVK_ANSI_3), NSEvent.ModifierFlags.command.union(.shift).rawValue,
            "Screenshot (entire screen)"
        ),
        (
            UInt16(kVK_ANSI_4), NSEvent.ModifierFlags.command.union(.shift).rawValue,
            "Screenshot (selection)"
        ),
        (
            UInt16(kVK_ANSI_5), NSEvent.ModifierFlags.command.union(.shift).rawValue,
            "Screenshot (window)"
        ),

        // Spotlight
        (UInt16(kVK_Space), NSEvent.ModifierFlags.command.rawValue, "Spotlight"),

        // Common app shortcuts
        (UInt16(kVK_ANSI_C), NSEvent.ModifierFlags.command.rawValue, "Copy"),
        (UInt16(kVK_ANSI_V), NSEvent.ModifierFlags.command.rawValue, "Paste"),
        (UInt16(kVK_ANSI_X), NSEvent.ModifierFlags.command.rawValue, "Cut"),
        (UInt16(kVK_ANSI_Z), NSEvent.ModifierFlags.command.rawValue, "Undo"),
        (UInt16(kVK_ANSI_Z), NSEvent.ModifierFlags.command.union(.shift).rawValue, "Redo"),
        (UInt16(kVK_ANSI_A), NSEvent.ModifierFlags.command.rawValue, "Select All"),
        (UInt16(kVK_ANSI_S), NSEvent.ModifierFlags.command.rawValue, "Save"),
        (UInt16(kVK_ANSI_P), NSEvent.ModifierFlags.command.rawValue, "Print"),
        (UInt16(kVK_ANSI_O), NSEvent.ModifierFlags.command.rawValue, "Open"),
        (UInt16(kVK_ANSI_N), NSEvent.ModifierFlags.command.rawValue, "New"),
        (UInt16(kVK_ANSI_W), NSEvent.ModifierFlags.command.rawValue, "Close Window"),
        (UInt16(kVK_ANSI_Q), NSEvent.ModifierFlags.command.rawValue, "Quit Application"),
        (UInt16(kVK_ANSI_F), NSEvent.ModifierFlags.command.rawValue, "Find"),
        (UInt16(kVK_ANSI_G), NSEvent.ModifierFlags.command.rawValue, "Find Next"),
        (UInt16(kVK_ANSI_G), NSEvent.ModifierFlags.command.union(.shift).rawValue, "Find Previous"),

        // Window management
        (UInt16(kVK_ANSI_M), NSEvent.ModifierFlags.command.rawValue, "Minimize Window"),
        (UInt16(kVK_Tab), NSEvent.ModifierFlags.command.rawValue, "Switch Application"),
        (
            UInt16(kVK_Tab), NSEvent.ModifierFlags.command.union(.shift).rawValue,
            "Switch Application (Reverse)"
        ),
        (UInt16(kVK_ANSI_Grave), NSEvent.ModifierFlags.command.rawValue, "Switch Window"),

        // Stage Manager
        (
            UInt16(kVK_ANSI_F), NSEvent.ModifierFlags.control.union(.command).rawValue,
            "Stage Manager"
        ),

        // Function keys
        (UInt16(kVK_F1), 0, "Brightness Down"),
        (UInt16(kVK_F2), 0, "Brightness Up"),
        (UInt16(kVK_F3), 0, "Mission Control"),
        (UInt16(kVK_F4), 0, "Launchpad"),
        (UInt16(kVK_F5), 0, "Keyboard Brightness Down"),
        (UInt16(kVK_F6), 0, "Keyboard Brightness Up"),
        (UInt16(kVK_F7), 0, "Previous Track"),
        (UInt16(kVK_F8), 0, "Play/Pause"),
        (UInt16(kVK_F9), 0, "Next Track"),
        (UInt16(kVK_F10), 0, "Mute"),
        (UInt16(kVK_F11), 0, "Volume Down"),
        (UInt16(kVK_F12), 0, "Volume Up"),
    ]

    /// Check if a shortcut conflicts with known system shortcuts
    /// - Parameters:
    ///   - keyCode: The key code to check
    ///   - modifiers: The modifier flags to check
    /// - Returns: Description of the conflicting system shortcut, or nil if no conflict
    func checkForSystemConflict(keyCode: UInt16, modifiers: UInt) -> String? {
        logger.debug(
            "Checking for system conflict - KeyCode: \(keyCode), Modifiers: \(modifiers)",
            service: serviceName,
            category: .shortcuts
        )

        // Create a MASShortcut object from the key code and modifiers
        // Convert UInt16 to Int for keyCode and UInt to NSEvent.ModifierFlags for modifiers
        let shortcutObj = MASShortcut(
            keyCode: Int(keyCode), modifierFlags: NSEvent.ModifierFlags(rawValue: modifiers))

        // We'll skip using MASShortcutValidator.isShortcutValid here since that's just checking
        // if the shortcut has valid modifiers, not if it conflicts with system shortcuts

        // Instead, we'll directly check for system conflicts using isShortcutAlreadyTaken
        var explanation: NSString?
        if MASShortcutValidator.shared().isShortcutAlreadyTaken(
            bySystem: shortcutObj, explanation: &explanation)
        {
            let conflictReason = explanation as String? ?? "Unknown system shortcut"
            logger.debug(
                "System conflict detected: \(conflictReason)",
                service: serviceName,
                category: .shortcuts
            )
            return conflictReason
        }

        logger.debug(
            "No system conflict detected by MASShortcutValidator",
            service: serviceName,
            category: .shortcuts
        )

        // Check against known system shortcuts as a fallback
        for shortcut in knownSystemShortcuts {
            if shortcut.keyCode == keyCode && shortcut.modifiers == modifiers {
                let conflictReason = "System: \(shortcut.description)"
                logger.debug(
                    "System conflict detected: \(conflictReason)",
                    service: serviceName,
                    category: .shortcuts
                )
                return conflictReason
            }
        }

        // Check for active application shortcuts
        if let appShortcut = checkForActiveApplicationShortcut(
            keyCode: keyCode, modifiers: modifiers)
        {
            return appShortcut
        }

        // Check for global hotkey managers
        if let hotkeyConflict = checkForGlobalHotkeyManagers(keyCode: keyCode, modifiers: modifiers)
        {
            return hotkeyConflict
        }

        return nil
    }

    /// Check if a shortcut conflicts with shortcuts in the active application
    /// - Parameters:
    ///   - keyCode: The key code to check
    ///   - modifiers: The modifier flags to check
    /// - Returns: Description of the conflicting application shortcut, or nil if no conflict
    private func checkForActiveApplicationShortcut(keyCode: UInt16, modifiers: UInt) -> String? {
        // Check for shortcuts in the active application
        if let activeApp = NSWorkspace.shared.frontmostApplication {
            if let conflict = checkForShortcutInApplication(
                keyCode: keyCode,
                modifiers: modifiers,
                application: activeApp
            ) {
                return "App: \(activeApp.localizedName ?? "Unknown") - \(conflict)"
            }
        }

        // Check for shortcuts in other running applications
        let runningApps = NSWorkspace.shared.runningApplications.filter {
            $0 != NSWorkspace.shared.frontmostApplication && $0.activationPolicy == .regular
        }

        for app in runningApps {
            if let conflict = checkForShortcutInApplication(
                keyCode: keyCode,
                modifiers: modifiers,
                application: app
            ) {
                return "App: \(app.localizedName ?? "Unknown") - \(conflict)"
            }
        }

        // Check for system-wide shortcuts in System Preferences
        if let systemConflict = checkForSystemPreferencesShortcut(
            keyCode: keyCode, modifiers: modifiers)
        {
            return "System Preferences: \(systemConflict)"
        }

        return nil
    }

    /// Check for shortcuts in a specific application
    /// - Parameters:
    ///   - keyCode: The key code to check
    ///   - modifiers: The modifier flags to check
    ///   - application: The application to check
    /// - Returns: Description of the conflicting shortcut, or nil if no conflict
    private func checkForShortcutInApplication(
        keyCode: UInt16, modifiers: UInt, application: NSRunningApplication
    ) -> String? {
        // This would require using the Carbon Menu Manager API to enumerate all menu items
        // and their shortcuts in the application, which is complex and beyond the scope
        // of this implementation.

        // For a more accurate implementation, we would need to:
        // 1. Get the application's menu bar
        // 2. Enumerate all menu items
        // 3. Check each menu item's shortcut against the provided shortcut

        // For now, we'll return nil as this would require more complex implementation
        return nil
    }

    /// Check for system-wide shortcuts in System Preferences
    /// - Parameters:
    ///   - keyCode: The key code to check
    ///   - modifiers: The modifier flags to check
    /// - Returns: Description of the conflicting shortcut, or nil if no conflict
    private func checkForSystemPreferencesShortcut(keyCode: UInt16, modifiers: UInt) -> String? {
        // This would require reading the system preferences for keyboard shortcuts
        // which is complex and beyond the scope of this implementation.

        // For a more accurate implementation, we would need to:
        // 1. Read the system preferences for keyboard shortcuts
        // 2. Check each shortcut against the provided shortcut

        // For now, we'll return nil as this would require more complex implementation
        return nil
    }

    /// Check for shortcuts in global hotkey managers like Alfred, BetterTouchTool, etc.
    /// - Parameters:
    ///   - keyCode: The key code to check
    ///   - modifiers: The modifier flags to check
    /// - Returns: Description of the conflicting shortcut, or nil if no conflict
    private func checkForGlobalHotkeyManagers(keyCode: UInt16, modifiers: UInt) -> String? {
        // This would require checking for shortcuts in global hotkey managers
        // which is complex and beyond the scope of this implementation.

        // For now, we'll return nil as this would require more complex implementation
        return nil
    }
}
