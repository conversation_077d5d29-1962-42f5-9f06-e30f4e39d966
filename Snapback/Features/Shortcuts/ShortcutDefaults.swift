import AppKit  // For NSEvent
import Carbon.HIToolbox
import Foundation

struct ShortcutConfig: Codable {
    var keyCode: UInt16
    var modifiers: UInt

    var displayString: String? {
        formatShortcut(keyCode: keyCode, modifiers: modifiers)
    }
}

class ShortcutDefaults {
    static let shared = ShortcutDefaults()

    // UserDefaults keys
    private let shortcutsKey = "CustomShortcuts"

    // Default shortcuts - Based on the image provided
    static let defaults: [String: ShortcutConfig] = [
        // Left, Right, Top, Bottom
        "leftHalf": ShortcutConfig(
            keyCode: UInt16(kVK_LeftArrow),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "rightHalf": ShortcutConfig(
            keyCode: UInt16(kVK_RightArrow),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "topHalf": ShortcutConfig(
            keyCode: UInt16(kVK_UpArrow),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "bottomHalf": ShortcutConfig(
            keyCode: UInt16(kVK_DownArrow),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),

        // Quarters
        "topLeftQuarter": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_U),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "topRightQuarter": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_I),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "bottomLeftQuarter": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_J),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "bottomRightQuarter": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_K),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),

        // Thirds
        "leftThird": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_D),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "centerThird": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_F),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "rightThird": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_G),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),

        // Two Thirds
        "leftTwoThirds": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_E),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "centerTwoThirds": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_R),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
        "rightTwoThirds": ShortcutConfig(
            keyCode: UInt16(kVK_ANSI_T),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),

        // Fullscreen (Maximize)
        "fullscreen": ShortcutConfig(
            keyCode: UInt16(kVK_Return),
            modifiers: NSEvent.ModifierFlags.control.union(.option).rawValue
        ),
    ]

    private var customShortcuts: [String: ShortcutConfig] {
        get {
            if let data = UserDefaults.standard.data(forKey: shortcutsKey),
                let decoded = try? JSONDecoder().decode([String: ShortcutConfig].self, from: data)
            {
                return decoded
            }
            return [:]
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: shortcutsKey)
            }
        }
    }

    func getShortcut(for action: String) -> ShortcutConfig {
        customShortcuts[action] ?? Self.defaults[action] ?? ShortcutConfig(keyCode: 0, modifiers: 0)
    }

    func setShortcut(for action: String, shortcut: ShortcutConfig) {
        var current = customShortcuts
        current[action] = shortcut
        customShortcuts = current
    }

    func resetShortcut(for action: String) {
        var current = customShortcuts
        current.removeValue(forKey: action)
        customShortcuts = current
    }

    func resetAllShortcuts() {
        customShortcuts = [:]
    }
}
