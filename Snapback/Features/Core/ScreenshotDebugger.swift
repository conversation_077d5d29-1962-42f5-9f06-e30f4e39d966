import AppKit
import Foundation

/// Utility class for drawing debug overlays for debugging purposes
class ScreenshotDebugger {
    static let shared = ScreenshotDebugger()
    private let logger = LoggingService.shared
    private let serviceName = "ScreenshotDebugger"

    // Store references to overlay windows to prevent them from being deallocated
    private var overlayWindows: [NSWindow] = []

    /// Draw a debug overlay on the screen to visualize the target rect
    /// This is currently disabled to prevent performance issues
    func drawDebugOverlay(rect: CGRect, color: NSColor = .red) {
        // Only log the rect, don't create an overlay window
        logger.info("Debug overlay (disabled) for rect: \(rect)", service: serviceName)

        // Disabled to prevent performance issues
        // Uncomment the following code to re-enable the overlay
        /*
        // Ensure we're on the main thread
        if Thread.isMainThread {
            createOverlayWindow(rect: rect, color: color)
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.createOverlayWindow(rect: rect, color: color)
            }
        }
        */
    }

    /// Create an overlay window on the main thread
    private func createOverlayWindow(rect: CGRect, color: NSColor) {
        // First, close any existing overlay windows to prevent accumulation
        clearAllOverlays()

        // Create a window for the overlay
        let overlay = NSWindow(
            contentRect: rect,
            styleMask: .borderless,
            backing: .buffered,
            defer: false
        )

        // Configure the window
        overlay.backgroundColor = color.withAlphaComponent(0.3)
        overlay.isOpaque = false
        overlay.hasShadow = false
        overlay.level = .floating
        overlay.ignoresMouseEvents = true

        // Store a reference to the window
        overlayWindows.append(overlay)

        // Show the window
        overlay.orderFront(nil)

        // Automatically close after 1.5 seconds and remove from array (reduced from 3 seconds)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            overlay.close()
            self?.overlayWindows.removeAll { $0 == overlay }
        }
    }

    /// Clear all existing overlay windows (private implementation)
    private func clearAllOverlays() {
        // Close all existing overlay windows
        for window in overlayWindows {
            window.close()
        }

        // Clear the array
        overlayWindows.removeAll()

        logger.debug("Cleared all overlay windows", service: serviceName)
    }

    /// Public method to clear all debug overlays
    func clearAllDebugOverlays() {
        // Ensure we're on the main thread
        if Thread.isMainThread {
            clearAllOverlays()
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.clearAllOverlays()
            }
        }

        logger.info("Manually cleared all debug overlays", service: serviceName)
    }
}
