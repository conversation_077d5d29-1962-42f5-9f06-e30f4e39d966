import AppKit
import Foundation

/// Utility class for debugging screen-related issues
class ScreenDebugger {
    static let shared = ScreenDebugger()
    private let logger = LoggingService.shared
    private let serviceName = "ScreenDebugger"
    
    /// Log detailed information about all screens
    func logScreenInfo() {
        logger.info("=== Screen Debug Information ===", service: serviceName)
        
        let screens = NSScreen.screens
        logger.info("Number of screens: \(screens.count)", service: serviceName)
        
        for (index, screen) in screens.enumerated() {
            logger.info("Screen \(index):", service: serviceName)
            logger.info("  Main screen: \(screen == NSScreen.main)", service: serviceName)
            logger.info("  Frame: \(screen.frame)", service: serviceName)
            logger.info("  Visible frame: \(screen.visibleFrame)", service: serviceName)
            logger.info("  Backing scale factor: \(screen.backingScaleFactor)", service: serviceName)
            logger.info("  Color space: \(screen.colorSpace?.localizedName ?? "Unknown")", service: serviceName)
            logger.info("  Depth: \(screen.depth)", service: serviceName)
            
            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber {
                logger.info("  Screen number: \(screenNumber)", service: serviceName)
            }
            
            logger.info("  Is landscape: \(screen.isLandscape)", service: serviceName)
            logger.info("  Is portrait: \(screen.isPortrait)", service: serviceName)
            
            // Calculate the difference between frame and visible frame
            let frameWidth = screen.frame.width
            let frameHeight = screen.frame.height
            let visibleWidth = screen.visibleFrame.width
            let visibleHeight = screen.visibleFrame.height
            let widthDiff = frameWidth - visibleWidth
            let heightDiff = frameHeight - visibleHeight
            
            logger.info("  Frame vs. Visible frame differences:", service: serviceName)
            logger.info("    Width difference: \(widthDiff) (\(widthDiff/frameWidth * 100)%)", service: serviceName)
            logger.info("    Height difference: \(heightDiff) (\(heightDiff/frameHeight * 100)%)", service: serviceName)
            logger.info("    Origin difference: (\(screen.visibleFrame.origin.x - screen.frame.origin.x), \(screen.visibleFrame.origin.y - screen.frame.origin.y))", service: serviceName)
            
            // Log adjusted visible frame
            let adjustedFrame = screen.adjustedVisibleFrame()
            logger.info("  Adjusted visible frame: \(adjustedFrame)", service: serviceName)
            logger.info("  Adjusted vs. Visible differences:", service: serviceName)
            logger.info("    Width difference: \(screen.visibleFrame.width - adjustedFrame.width)", service: serviceName)
            logger.info("    Height difference: \(screen.visibleFrame.height - adjustedFrame.height)", service: serviceName)
            logger.info("    Origin difference: (\(adjustedFrame.origin.x - screen.visibleFrame.origin.x), \(adjustedFrame.origin.y - screen.visibleFrame.origin.y))", service: serviceName)
        }
        
        logger.info("=== End Screen Debug Information ===", service: serviceName)
    }
}