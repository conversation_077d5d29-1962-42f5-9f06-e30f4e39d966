//
//  SandboxUtility.swift
//  Snapback
//
//  Created by Snapback on 2025-06-23.
//

import Foundation
import AppKit

/// Utility class for sandbox detection and compatibility
class SandboxUtility {
    
    /// Shared instance
    static let shared = SandboxUtility()
    
    private init() {}
    
    // MARK: - Sandbox Detection
    
    /// Check if the app is running in a sandboxed environment
    var isSandboxed: Bool {
        // Method 1: Check for sandbox container ID
        if ProcessInfo.processInfo.environment["APP_SANDBOX_CONTAINER_ID"] != nil {
            return true
        }
        
        // Method 2: Check for sandbox entitlements
        if let entitlements = getEntitlements(),
           let sandboxEnabled = entitlements["com.apple.security.app-sandbox"] as? Bool {
            return sandboxEnabled
        }
        
        // Method 3: Check if we can write to restricted locations
        let testPath = "/tmp/snapback_sandbox_test"
        let canWrite = FileManager.default.createFile(atPath: testPath, contents: nil, attributes: nil)
        if canWrite {
            try? FileManager.default.removeItem(atPath: testPath)
        }
        
        return !canWrite
    }
    
    /// Get the app's entitlements
    private func getEntitlements() -> [String: Any]? {
        guard let task = SecTaskCreateFromSelf(nil) else { return nil }
        
        var error: Unmanaged<CFError>?
        guard let entitlements = SecTaskCopyValueForEntitlement(
            task, "com.apple.security.app-sandbox" as CFString, &error
        ) else {
            return nil
        }
        
        return entitlements as? [String: Any]
    }
    
    // MARK: - Accessibility Compatibility
    
    /// Check if accessibility APIs are available and working
    func checkAccessibilityCompatibility() -> AccessibilityCompatibilityResult {
        let logger = LoggingService.shared
        
        // Check basic permission
        let hasPermission = AXIsProcessTrusted()
        
        // Test functionality
        var canCreateElements = false
        var canAccessWindows = false
        var canModifyWindows = false
        
        if hasPermission {
            // Test if we can create accessibility elements
            if let frontmostApp = NSWorkspace.shared.frontmostApplication {
                let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)
                
                // Test window access
                var windowsRef: CFTypeRef?
                let windowsError = AXUIElementCopyAttributeValue(
                    appElement, kAXWindowsAttribute as CFString, &windowsRef)
                
                canCreateElements = true
                canAccessWindows = windowsError == .success
                
                // Test window modification (if we have windows)
                if canAccessWindows, let windows = windowsRef as? [AXUIElement], !windows.isEmpty {
                    let testWindow = windows[0]
                    
                    // Try to get current position (read test)
                    var positionRef: CFTypeRef?
                    let positionError = AXUIElementCopyAttributeValue(
                        testWindow, kAXPositionAttribute as CFString, &positionRef)
                    
                    if positionError == .success {
                        // Try to set the same position (write test)
                        let setError = AXUIElementSetAttributeValue(
                            testWindow, kAXPositionAttribute as CFString, positionRef!)
                        canModifyWindows = setError == .success
                    }
                }
            }
        }
        
        let result = AccessibilityCompatibilityResult(
            hasPermission: hasPermission,
            canCreateElements: canCreateElements,
            canAccessWindows: canAccessWindows,
            canModifyWindows: canModifyWindows,
            isSandboxed: isSandboxed
        )
        
        logger.info(
            "🔍 SANDBOX DIAGNOSTIC: Accessibility compatibility check - \(result)",
            service: "SandboxUtility"
        )
        
        return result
    }
    
    // MARK: - Troubleshooting
    
    /// Generate a comprehensive diagnostic report
    func generateDiagnosticReport() -> String {
        let compatibility = checkAccessibilityCompatibility()
        
        var report = """
        === Snapback Sandbox Diagnostic Report ===
        
        Environment:
        - Sandboxed: \(isSandboxed)
        - macOS Version: \(ProcessInfo.processInfo.operatingSystemVersionString)
        - App Version: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
        
        Accessibility Status:
        - Has Permission: \(compatibility.hasPermission)
        - Can Create Elements: \(compatibility.canCreateElements)
        - Can Access Windows: \(compatibility.canAccessWindows)
        - Can Modify Windows: \(compatibility.canModifyWindows)
        
        Entitlements:
        """
        
        // Add entitlement information
        if let entitlements = getEntitlements() {
            for (key, value) in entitlements {
                report += "\n- \(key): \(value)"
            }
        } else {
            report += "\n- Unable to read entitlements"
        }
        
        // Add recommendations
        report += "\n\nRecommendations:"
        
        if !compatibility.hasPermission {
            report += "\n- Grant accessibility permissions in System Settings"
        }
        
        if compatibility.hasPermission && !compatibility.canAccessWindows {
            report += "\n- Check entitlements for com.apple.security.automation.apple-events"
            report += "\n- Verify temporary exceptions for system events"
        }
        
        if compatibility.canAccessWindows && !compatibility.canModifyWindows {
            report += "\n- Window modification may be restricted by sandbox"
            report += "\n- Check for additional entitlements needed"
        }
        
        if isSandboxed && !compatibility.canModifyWindows {
            report += "\n- Sandboxed apps may need additional setup"
            report += "\n- Consider using temporary exceptions for full functionality"
        }
        
        return report
    }
}

// MARK: - Supporting Types

/// Result of accessibility compatibility check
struct AccessibilityCompatibilityResult: CustomStringConvertible {
    let hasPermission: Bool
    let canCreateElements: Bool
    let canAccessWindows: Bool
    let canModifyWindows: Bool
    let isSandboxed: Bool
    
    var isFullyCompatible: Bool {
        return hasPermission && canCreateElements && canAccessWindows && canModifyWindows
    }
    
    var description: String {
        return "Permission: \(hasPermission), Elements: \(canCreateElements), Access: \(canAccessWindows), Modify: \(canModifyWindows), Sandboxed: \(isSandboxed)"
    }
}
