import SwiftUI

extension View {
    /// Show a success toast
    func showToast(success title: String, message: String? = nil, duration: TimeInterval = 3.0) -> some View {
        ToastManager.shared.showSuccess(title: title, message: message, duration: duration)
        return self
    }
    
    /// Show an error toast
    func showToast(error title: String, message: String? = nil, duration: TimeInterval = 3.0) -> some View {
        ToastManager.shared.showError(title: title, message: message, duration: duration)
        return self
    }
    
    /// Show an info toast
    func showToast(info title: String, message: String? = nil, duration: TimeInterval = 3.0) -> some View {
        ToastManager.shared.showInfo(title: title, message: message, duration: duration)
        return self
    }
    
    /// Show a loading toast
    func showToast(loading title: String, message: String? = nil) -> some View {
        ToastManager.shared.showLoading(title: title, message: message)
        return self
    }
    
    /// Update a loading toast
    func updateToast(loading title: String, message: String? = nil) -> some View {
        ToastManager.shared.updateLoading(title: title, message: message)
        return self
    }
    
    /// Complete a loading toast with success
    func completeToast(success title: String, message: String? = nil, duration: TimeInterval = 3.0) -> some View {
        ToastManager.shared.completeLoading(title: title, message: message, duration: duration)
        return self
    }
    
    /// Complete a loading toast with error
    func failToast(error title: String, message: String? = nil, duration: TimeInterval = 3.0) -> some View {
        ToastManager.shared.failLoading(title: title, message: message, duration: duration)
        return self
    }
    
    /// Dismiss the current toast
    func dismissToast() -> some View {
        ToastManager.shared.dismiss()
        return self
    }
}
