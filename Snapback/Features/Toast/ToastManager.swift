import Combine
import SwiftUI

/// Toast notification types
enum ToastType {
    case success
    case error
    case info
    case loading

    var icon: String {
        switch self {
        case .success: return "checkmark.circle.fill"
        case .error: return "exclamationmark.circle.fill"
        case .info: return "info.circle.fill"
        case .loading: return "circle.dotted"
        }
    }

    var color: Color {
        switch self {
        case .success: return .green
        case .error: return .red
        case .info: return .blue
        case .loading: return .gray
        }
    }
}

/// Toast data model
struct ToastData: Identifiable, Equatable {
    let id = UUID()
    let type: ToastType
    let title: String
    let message: String?
    let duration: TimeInterval

    static func == (lhs: ToastData, rhs: ToastData) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Global toast manager
class ToastManager: ObservableObject {
    static let shared = ToastManager()

    @Published var currentToast: ToastData?
    @Published var isPresented: Bool = false

    private var dismissTimer: Timer?
    private var cancellables = Set<AnyCancellable>()

    private init() {
        // Monitor changes to currentToast
        $currentToast
            .sink { [weak self] toast in
                if toast != nil {
                    self?.isPresented = true
                }
            }
            .store(in: &cancellables)
    }

    /// Show a success toast
    func showSuccess(title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        show(type: .success, title: title, message: message, duration: duration)
    }

    /// Show an error toast
    func showError(title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        show(type: .error, title: title, message: message, duration: duration)
    }

    /// Show an info toast
    func showInfo(title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        show(type: .info, title: title, message: message, duration: duration)
    }

    /// Show a loading toast
    func showLoading(title: String, message: String? = nil) {
        show(type: .loading, title: title, message: message, duration: 0)  // No auto-dismiss for loading
    }

    /// Update an existing loading toast
    func updateLoading(title: String, message: String? = nil) {
        guard let toast = currentToast, toast.type == .loading else { return }
        currentToast = ToastData(type: .loading, title: title, message: message, duration: 0)
    }

    /// Dismiss the current toast
    func dismiss() {
        isPresented = false

        // Clear the toast after animation completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.currentToast = nil
        }

        // Cancel any pending dismiss timer
        dismissTimer?.invalidate()
        dismissTimer = nil
    }

    /// Complete a loading toast with success
    func completeLoading(title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        guard currentToast?.type == .loading else { return }
        show(type: .success, title: title, message: message, duration: duration)
    }

    /// Complete a loading toast with error
    func failLoading(title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        guard currentToast?.type == .loading else { return }
        show(type: .error, title: title, message: message, duration: duration)
    }

    // MARK: - Private Methods

    private func show(type: ToastType, title: String, message: String?, duration: TimeInterval) {
        // Check if toast notifications are enabled
        guard DefaultsManager.shared.showToastNotifications else {
            return
        }

        // Cancel any existing timer
        dismissTimer?.invalidate()

        // Create and show the new toast
        currentToast = ToastData(type: type, title: title, message: message, duration: duration)

        // Set up auto-dismiss timer if duration > 0
        if duration > 0 {
            dismissTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) {
                [weak self] _ in
                self?.dismiss()
            }
        }
    }
}
