<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- Enable App Sandbox -->
	<key>com.apple.security.app-sandbox</key>
	<true/>

	<!-- File Access for Workspace Import/Export -->
	<key>com.apple.security.files.user-selected.read-write</key>
	<true/>

	<!-- CRITICAL: Accessibility Access for Window Management -->
	<key>com.apple.security.automation.apple-events</key>
	<true/>

	<!-- Apple Events Access for System Integration -->
	<key>com.apple.security.temporary-exception.apple-events</key>
	<array>
		<string>com.apple.systemevents</string>
		<string>com.apple.finder</string>
		<string>com.apple.dock</string>
	</array>

	<!-- Network Access for CloudKit Sync (if needed) -->
	<key>com.apple.security.network.client</key>
	<true/>

	<!-- Application Data Access for Window Management -->
	<key>com.apple.security.temporary-exception.shared-preference.read-write</key>
	<array>
		<string>com.apple.systempreferences</string>
	</array>

	<!-- System Events Access for Window Operations -->
	<key>com.apple.security.scripting-targets</key>
	<dict>
		<key>com.apple.systemevents</key>
		<array>
			<string>com.apple.systemevents.window-management</string>
		</array>
	</dict>
</dict>
</plist>
