//
//  SnapbackApp.swift
//  Snapback
//
//  Created by <PERSON> on 23/01/25.
//
import ApplicationServices
import Combine
import CoreGraphics
import SwiftUI

@main
struct SnapbackApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            SettingsView()
                .environmentObject(appDelegate)
                .environmentObject(appDelegate.workspaceService)
        }
    }
}

// Helper extension
extension NSMenuItem {
    convenience init(
        title: String, action: Selector?, keyEquivalent: String, modifier: NSEvent.ModifierFlags
    ) {
        self.init(title: title, action: action, keyEquivalent: keyEquivalent)
        self.keyEquivalentModifierMask = modifier
    }

}
