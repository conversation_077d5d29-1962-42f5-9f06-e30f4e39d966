# Snapback Technical Overview

## Executive Summary

Snapback is a sophisticated macOS workspace management application that enables power users to save, restore, and switch between different work contexts seamlessly. Built with SwiftUI and AppKit, Snapback combines workspace save/restore functionality with optional window management features, providing a comprehensive solution for users who need to manage multiple work environments efficiently.

## Core Functionality

### 1. Workspace Save & Restore System

**Workspace Definition**
- A workspace captures the complete state of your desktop environment
- Includes window positions, sizes, and associated applications
- Stores display arrangement metadata for multi-monitor setups
- Supports custom layouts and window groupings

**Save Process**
```swift
// Core workspace structure
struct Workspace: Codable, Identifiable {
    let id: UUID
    var name: String
    var windowInfos: [WindowInfo]
    var shortcutKeyCode: UInt16?
    var shortcutModifiers: UInt?
    var displayArrangement: DisplayArrangementInfo?
    var customLayout: CustomLayout?
}
```

**Technical Implementation**
- Uses Accessibility APIs to capture window information
- Normalizes coordinates across different display configurations
- Stores data locally in UserDefaults with JSON encoding
- Implements two-phase restoration: app launching followed by window positioning

### 2. Window Management (Optional)

**Drag-to-Snap System**
- Real-time window snapping when dragging to screen edges
- Configurable modifier keys (Option key default)
- Smart screen detection using Rectangle's proven algorithms
- Supports multiple monitor configurations

**Snapping Positions**
- Half-screen snapping (left, right, top, bottom)
- Quarter-screen corner snapping
- Custom layout positioning
- Maintains aspect ratios and minimum window sizes

### 3. Keyboard Shortcuts System

**Global Shortcuts**
- Save current workspace: Customizable hotkey
- Restore specific workspaces: Individual shortcuts per workspace
- Window management shortcuts: When enabled
- Conflict detection with system shortcuts

**Implementation Details**
- Uses MASShortcut framework for reliable shortcut handling
- Stores shortcuts in both workspace model and KeyboardShortcuts system
- Automatic migration of legacy shortcut formats
- System conflict avoidance when enabled

## Architecture Overview

### Service Layer Architecture

```
AppDelegate (Coordinator)
├── WorkspaceService (Core workspace management)
├── WindowSnappingService (Window positioning)
├── ShortcutService (Keyboard shortcut handling)
├── SnappingManager (Drag-to-snap functionality)
└── WindowManager (UI window management)
```

### Key Components

**WorkspaceService**
- Manages CRUD operations for workspaces
- Handles workspace restoration logic
- Implements display migration for monitor changes
- Provides reactive updates via @Published properties

**WindowSnappingService**
- Interfaces with Accessibility APIs
- Calculates optimal window positions
- Handles multi-monitor scenarios
- Provides error handling for accessibility permissions

**SnappingManager**
- Monitors mouse drag events
- Implements real-time snap feedback
- Manages snap zones and trigger areas
- Configurable via user preferences

## Data Management

### Local Storage
- Primary storage: UserDefaults with JSON serialization
- Automatic backup and recovery mechanisms
- Migration support for data format changes
- Immediate synchronization after changes

### Import/Export System
- JSON-based workspace export format
- Conflict resolution for duplicate names/shortcuts
- Batch import with user confirmation
- Version compatibility checking

### Future: CloudKit Sync (Planned)
- Cross-device workspace synchronization
- Conflict resolution for concurrent edits
- Offline-first architecture with sync when available
- Settings and shortcuts sync across devices

## User Interface

### Settings Architecture
- Tab-based settings interface
- General tab: Core preferences and workspace management
- Window Management tab: Snapping and positioning settings
- Conditional UI: Hides disabled features completely

### Workspace Management UI
- Visual workspace previews with app icons
- Drag-and-drop workspace reordering
- Inline editing of workspace names and shortcuts
- Real-time shortcut conflict detection

### Toast Notification System
- Non-intrusive status updates
- Loading states for long operations
- Error reporting with actionable messages
- Success confirmations with details

## Technical Requirements

### System Requirements
- macOS 12.0+ (Monterey or later)
- Accessibility permissions for window management
- Optional: Screen Recording permission for enhanced features

### Performance Characteristics
- Sub-second workspace restoration for typical setups
- Minimal memory footprint (~50MB typical usage)
- Efficient window detection using cached app information
- Optimized for multi-monitor configurations

### Security & Privacy
- Local-first data storage
- No network communication (current version)
- Accessibility API usage limited to window management
- User-controlled feature enablement

## Development Architecture

### Code Organization
```
Snapback/
├── App/ (Application lifecycle)
├── Features/
│   ├── Workspace/ (Core workspace functionality)
│   ├── WindowManagement/ (Snapping and positioning)
│   ├── Settings/ (User preferences)
│   ├── UI/ (Interface components)
│   └── Logging/ (Comprehensive logging system)
```

### Logging System
- Structured logging with categories and services
- Xcode-friendly log filtering
- Debug information for troubleshooting
- Performance metrics and timing data

### Testing Strategy
- Unit tests for core business logic
- Integration tests for workspace restoration
- UI tests for critical user workflows
- Performance tests for large workspace sets

## Integration Points

### System Integration
- Menu bar presence with quick access
- Launch at login capability
- System shortcut conflict detection
- Accessibility API integration

### Third-Party Dependencies
- MASShortcut: Reliable global shortcut handling
- KeyboardShortcuts: Modern shortcut management
- Rectangle algorithms: Proven window positioning logic

## Future Roadmap

### Planned Features
1. **CloudKit Sync**: Cross-device workspace synchronization
2. **Advanced Layouts**: Custom window arrangements and templates
3. **App-Specific Rules**: Per-application positioning preferences
4. **Workspace Automation**: Time-based or trigger-based workspace switching
5. **Enhanced Previews**: Live window thumbnails in workspace manager

### Technical Improvements
- Performance optimization for large workspace collections
- Enhanced multi-monitor support
- Improved accessibility API error handling
- Advanced conflict resolution algorithms

## Conclusion

Snapback represents a mature, well-architected solution for workspace management on macOS. Its modular design, comprehensive logging, and user-centric approach make it both powerful for advanced users and maintainable for ongoing development. The optional window management features and planned CloudKit sync position it as a comprehensive productivity tool for power users who need to manage complex, multi-application work environments efficiently.
