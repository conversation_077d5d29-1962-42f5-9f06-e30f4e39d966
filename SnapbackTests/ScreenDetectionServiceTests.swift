import XCTest

@testable import Snapback

final class ScreenDetectionServiceTests: XCTestCase {
    var screenDetectionService: ScreenDetectionService!

    override func setUp() {
        super.setUp()
        screenDetectionService = ScreenDetectionService()
    }

    override func tearDown() {
        screenDetectionService = nil
        super.tearDown()
    }

    // MARK: - Helper Methods

    private func createMockScreen(frame: CGRect) -> TestMockNSScreen {
        let screen = TestMockNSScreen()
        screen.mockFrame = frame
        return screen
    }

    // MARK: - Single Screen Tests

    func testGetScreenContainingWithSingleScreen() {
        // Given
        let screenFrame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let mockScreen = createMockScreen(frame: screenFrame)

        // Create a test harness that uses our mock screen
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen])

        // When - Window is fully contained within the screen
        let windowFrame = CGRect(x: 100, y: 100, width: 500, height: 400)
        let result = testHarness.getScreenContaining(windowFrame)

        // Then
        XCTAssertNotNil(result, "Should find a screen containing the window")
        XCTAssertEqual(result, mockScreen, "Should return the mock screen")
    }

    func testGetScreenContainingWithWindowAtEdge() {
        // Given
        let screenFrame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let mockScreen = createMockScreen(frame: screenFrame)

        // Create a test harness that uses our mock screen
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen])

        // When - Window is at the edge of the screen
        let windowFrame = CGRect(x: 0, y: 0, width: 500, height: 400)
        let result = testHarness.getScreenContaining(windowFrame)

        // Then
        XCTAssertNotNil(result, "Should find a screen containing the window at the edge")
        XCTAssertEqual(result, mockScreen, "Should return the mock screen")
    }

    func testGetScreenContainingWithWindowPartiallyOutside() {
        // Given
        let screenFrame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let mockScreen = createMockScreen(frame: screenFrame)

        // Create a test harness that uses our mock screen
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen])

        // When - Window is partially outside the screen
        let windowFrame = CGRect(x: -200, y: 100, width: 500, height: 400)
        let result = testHarness.getScreenContaining(windowFrame)

        // Then
        XCTAssertNotNil(result, "Should find a screen containing part of the window")
        XCTAssertEqual(result, mockScreen, "Should return the mock screen")
    }

    // MARK: - Multiple Screen Tests

    func testGetScreenContainingWithTwoScreens() {
        // Given
        let screen1Frame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let screen2Frame = CGRect(x: 1000, y: 0, width: 1000, height: 800)

        let mockScreen1 = createMockScreen(frame: screen1Frame)
        let mockScreen2 = createMockScreen(frame: screen2Frame)

        // Create a test harness that uses our mock screens
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen1, mockScreen2])

        // When - Window is fully contained within the first screen
        let windowFrame1 = CGRect(x: 100, y: 100, width: 500, height: 400)
        let result1 = testHarness.getScreenContaining(windowFrame1)

        // Then
        XCTAssertNotNil(result1, "Should find a screen containing the window")
        XCTAssertEqual(result1, mockScreen1, "Should return the first mock screen")

        // When - Window is fully contained within the second screen
        let windowFrame2 = CGRect(x: 1100, y: 100, width: 500, height: 400)
        let result2 = testHarness.getScreenContaining(windowFrame2)

        // Then
        XCTAssertNotNil(result2, "Should find a screen containing the window")
        XCTAssertEqual(result2, mockScreen2, "Should return the second mock screen")
    }

    func testGetScreenContainingWithWindowSpanningTwoScreens() {
        // Given
        let screen1Frame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let screen2Frame = CGRect(x: 1000, y: 0, width: 1000, height: 800)

        let mockScreen1 = createMockScreen(frame: screen1Frame)
        let mockScreen2 = createMockScreen(frame: screen2Frame)

        // Create a test harness that uses our mock screens
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen1, mockScreen2])

        // When - Window spans both screens but has more area in the first screen
        let windowFrame = CGRect(x: 800, y: 100, width: 400, height: 400)
        let result = testHarness.getScreenContaining(windowFrame)

        // Then
        XCTAssertNotNil(result, "Should find a screen containing the window")
        XCTAssertEqual(result, mockScreen1, "Should return the first mock screen (more area)")

        // When - Window spans both screens but has more area in the second screen
        let windowFrame2 = CGRect(x: 900, y: 100, width: 400, height: 400)
        let result2 = testHarness.getScreenContaining(windowFrame2)

        // Then
        XCTAssertNotNil(result2, "Should find a screen containing the window")
        XCTAssertEqual(result2, mockScreen2, "Should return the second mock screen (more area)")
    }

    func testGetScreenContainingWithVerticalScreenArrangement() {
        // Given
        let screen1Frame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let screen2Frame = CGRect(x: 0, y: 800, width: 1000, height: 800)

        let mockScreen1 = createMockScreen(frame: screen1Frame)
        let mockScreen2 = createMockScreen(frame: screen2Frame)

        // Create a test harness that uses our mock screens
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen1, mockScreen2])

        // When - Window is fully contained within the first screen
        let windowFrame1 = CGRect(x: 100, y: 100, width: 500, height: 400)
        let result1 = testHarness.getScreenContaining(windowFrame1)

        // Then
        XCTAssertNotNil(result1, "Should find a screen containing the window")
        XCTAssertEqual(result1, mockScreen1, "Should return the first mock screen")

        // When - Window is fully contained within the second screen
        let windowFrame2 = CGRect(x: 100, y: 900, width: 500, height: 400)
        let result2 = testHarness.getScreenContaining(windowFrame2)

        // Then
        XCTAssertNotNil(result2, "Should find a screen containing the window")
        XCTAssertEqual(result2, mockScreen2, "Should return the second mock screen")
    }

    // MARK: - Edge Cases

    func testGetScreenContainingWithWindowOutsideAllScreens() {
        // Given
        let screen1Frame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let screen2Frame = CGRect(x: 1000, y: 0, width: 1000, height: 800)

        let mockScreen1 = createMockScreen(frame: screen1Frame)
        let mockScreen2 = createMockScreen(frame: screen2Frame)

        // Create a test harness that uses our mock screens
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen1, mockScreen2])

        // When - Window is completely outside all screens
        let windowFrame = CGRect(x: 0, y: 1000, width: 500, height: 400)
        let result = testHarness.getScreenContaining(windowFrame)

        // Then
        // The implementation should return the main screen as a fallback
        XCTAssertNotNil(result, "Should return a fallback screen")
    }

    func testGetAllScreens() {
        // Given
        let screen1Frame = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let screen2Frame = CGRect(x: 1000, y: 0, width: 1000, height: 800)

        let mockScreen1 = createMockScreen(frame: screen1Frame)
        let mockScreen2 = createMockScreen(frame: screen2Frame)

        // Create a test harness that uses our mock screens
        let testHarness = ScreenDetectionTestHarness(screens: [mockScreen1, mockScreen2])

        // When
        let result = testHarness.getAllScreens()

        // Then
        XCTAssertEqual(result.count, 2, "Should return all screens")
        XCTAssertTrue(result.contains(mockScreen1), "Should contain the first mock screen")
        XCTAssertTrue(result.contains(mockScreen2), "Should contain the second mock screen")
    }
}

// MARK: - Test Harness

class ScreenDetectionTestHarness: ScreenDetectable {
    private let mockScreens: [NSScreen]

    init(screens: [NSScreen]) {
        self.mockScreens = screens
    }

    func getScreenContaining(_ frame: CGRect) -> NSScreen? {
        var result: NSScreen? = mockScreens.first
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        // Convert to flipped coordinates for screen detection
        let normalizedRect = frame.screenFlipped

        // First check if any screen fully contains the rect
        for screen in mockScreens {
            let currentFrameOfScreen = screen.frame

            if currentFrameOfScreen.contains(normalizedRect) {
                result = screen
                break
            }

            // If no screen fully contains the rect, find the screen with the largest percentage
            let percentageOfRectWithinCurrentFrameOfScreen = percentageOf(
                normalizedRect, withinFrameOfScreen: currentFrameOfScreen)

            if percentageOfRectWithinCurrentFrameOfScreen
                > largestPercentageOfRectWithinFrameOfScreen
            {
                largestPercentageOfRectWithinFrameOfScreen =
                    percentageOfRectWithinCurrentFrameOfScreen
                result = screen
            }
        }

        return result
    }

    func getAllScreens() -> [NSScreen] {
        return mockScreens
    }

    func detectScreens(using windowElement: AXUIElement?) -> UsableScreens? {
        // Return a basic UsableScreens for testing
        guard let firstScreen = mockScreens.first else { return nil }
        return UsableScreens(
            screens: mockScreens,
            currentScreen: firstScreen
        )
    }

    /// Calculate the percentage of a rectangle that is within a screen's frame
    private func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect) -> CGFloat
    {
        let intersectionOfRectAndFrameOfScreen = rect.intersection(frameOfScreen)
        if intersectionOfRectAndFrameOfScreen.isNull {
            return 0.0
        }

        let areaOfRect = rect.width * rect.height
        if areaOfRect == 0.0 {
            return 0.0
        }

        let areaOfIntersectionOfRectAndFrameOfScreen =
            intersectionOfRectAndFrameOfScreen.width * intersectionOfRectAndFrameOfScreen.height
        return areaOfIntersectionOfRectAndFrameOfScreen / areaOfRect
    }
}

// MARK: - Mock Classes
