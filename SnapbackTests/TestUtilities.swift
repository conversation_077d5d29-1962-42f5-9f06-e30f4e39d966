import Combine
import XCTest

@testable import Snapback

// MARK: - Mock Classes for Testing

class TestMockNSScreen: NSScreen {
    var mockFrame: CGRect = .zero

    override var frame: CGRect {
        return mockFrame
    }

    override var visibleFrame: CGRect {
        return mockFrame
    }
}

class TestMockAccessibilityElement: AccessibilityElement {
    var mockWindowInfo: WindowInfo?
    var mockFrontmostWindow: AXUIElement?
    var shouldThrowError = false
    var shouldThrowErrorOnSetFrame = false
    var lastWindow: AXUIElement?
    var lastSetFrame: CGRect?
    var frameAfterSet: CGRect?
    var setFrameCallCount = 0
    var shouldReturnCorrectFrameAfterAttempts = 1
    var currentAttempt = 0

    override func windowInfo(for window: AXUIElement) async throws -> WindowInfo {
        if shouldThrowError {
            throw AccessibilityError.failedToGetAttribute(.cannotComplete)
        }
        lastWindow = window
        if let info = mockWindowInfo {
            return info
        }
        throw AccessibilityError.failedToGetAttribute(.cannotComplete)
    }

    override func setFrame(_ window: AXUIElement, _ frame: CGRect, isBottomAligned: Bool = false)
        async throws
    {
        if shouldThrowErrorOnSetFrame {
            throw AccessibilityError.failedToSetAttribute(.cannotComplete)
        }

        lastWindow = window
        lastSetFrame = frame
        setFrameCallCount += 1
        currentAttempt += 1
    }

    override func getFrame(_ window: AXUIElement) async throws -> CGRect {
        lastWindow = window

        if let frameAfterSet = frameAfterSet, currentAttempt < shouldReturnCorrectFrameAfterAttempts
        {
            return frameAfterSet
        }

        return lastSetFrame ?? CGRect.zero
    }

    // Mock method to simulate getFrontmostWindow
    func getFrontmostWindow() async throws -> AXUIElement? {
        return mockFrontmostWindow
    }
}

class TestMockCalculationService: WindowCalculationService {
    var calculatedRect: CGRect = .zero

    func calculateWindowRect(for direction: WindowDirection, window: WindowInfo, screen: NSScreen)
        -> CGRect
    {
        return calculatedRect
    }
}

class TestMockScreenDetection: ScreenDetectionService {
    var targetScreen: NSScreen?

    override func getScreenContaining(_ frame: CGRect) -> NSScreen? {
        return targetScreen
    }
}

// MARK: - Test Helpers

extension XCTestCase {
    func waitForPublisher<T: Publisher>(
        _ publisher: T,
        timeout: TimeInterval = 1.0
    ) -> T.Output? where T.Failure == Never {
        let expectation = expectation(description: "Waiting for publisher")
        var result: T.Output?

        let cancellable =
            publisher
            .sink { value in
                result = value
                expectation.fulfill()
            }

        wait(for: [expectation], timeout: timeout)
        cancellable.cancel()

        return result
    }
}
