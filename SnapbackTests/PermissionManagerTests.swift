import Combine
import XCTest

@testable import Snapback

final class PermissionManagerTests: XCTestCase {
    var permissionManager: PermissionManager!
    private var cancellables = Set<AnyCancellable>()

    override func setUp() {
        super.setUp()
        permissionManager = PermissionManager.shared
    }

    override func tearDown() {
        cancellables.removeAll()
        super.tearDown()
    }

    // MARK: - Permission Status Tests

    func testCheckAccessibilityPermission() {
        // When
        let result = permissionManager.checkAccessibilityPermission()

        // Then - Just verify the method runs without crashing
        // We can't reliably test the actual result since it depends on system permissions
        XCTAssertNotNil(result)
    }

    // MARK: - Notification Tests

    func testPermissionStatusPublisher() {
        // This test just verifies that the published property exists and can be subscribed to
        let expectation = XCTestExpectation(
            description: "Should be able to subscribe to permission status")

        // When
        let cancellable = permissionManager.$isAccessibilityPermissionGranted
            .sink { _ in
                expectation.fulfill()
            }

        // Then
        wait(for: [expectation], timeout: 1.0)
        cancellable.cancel()
    }

    // MARK: - Permission Launch Bug Fix Tests

    func testPermissionManagerAlwaysPerformsFreshCheckOnInitialization() {
        // This test verifies the fix for the bug where cached permission state
        // prevented fresh verification on app launch

        // Given - Simulate a scenario where permissions were previously granted
        // but have since been revoked (this is the bug scenario)
        UserDefaults.standard.set(true, forKey: "hasEverBeenGranted")
        UserDefaults.standard.set(true, forKey: "lastKnownStatus")
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "lastCheckTimestamp")

        // When - Force a fresh check (simulating the fixed initialization behavior)
        let result = permissionManager.forceFreshPermissionCheck()

        // Then - The result should reflect actual system state, not cached state
        // In test environment, permissions are typically denied
        XCTAssertFalse(
            result, "Fresh permission check should reflect actual system state, not cached state")

        // Cleanup
        permissionManager.clearPersistentState()
    }

    func testForceFreshPermissionCheckDetectsStateChanges() {
        // Given - Clear any existing state
        permissionManager.clearPersistentState()
        let initialState = permissionManager.isAccessibilityPermissionGranted

        // When - Force a fresh check
        let freshResult = permissionManager.forceFreshPermissionCheck()

        // Then - Result should match actual system state
        XCTAssertEqual(freshResult, permissionManager.isAccessibilityPermissionGranted)

        // Cleanup
        permissionManager.clearPersistentState()
    }

    func testPersistentStateIsUpdatedAfterFreshCheck() {
        // Given - Clear any existing state
        permissionManager.clearPersistentState()

        // When - Perform a fresh check
        let result = permissionManager.checkAccessibilityPermission()

        // Then - Persistent state should be updated
        let persistentState = permissionManager.getPersistentState()
        XCTAssertEqual(persistentState.lastKnownStatus, result)
        XCTAssertTrue(persistentState.lastCheckTimestamp > 0)

        // Cleanup
        permissionManager.clearPersistentState()
    }

    func testAppLaunchScenarioWithRevokedPermissions() {
        // This test simulates the exact bug scenario:
        // 1. User grants permissions (cached as granted)
        // 2. User revokes permissions in System Settings
        // 3. User quits and relaunches app
        // 4. App should detect revoked permissions and show dialog

        // Given - Simulate previously granted permissions in cache
        UserDefaults.standard.set(true, forKey: "hasEverBeenGranted")
        UserDefaults.standard.set(true, forKey: "lastKnownStatus")
        UserDefaults.standard.set(Date().timeIntervalSince1970 - 3600, forKey: "lastCheckTimestamp")  // 1 hour ago

        // When - Simulate app launch by forcing a fresh check
        // (In the actual fix, this happens during PermissionManager init)
        let actualPermissionState = permissionManager.forceFreshPermissionCheck()

        // Then - The result should reflect actual system state, not cached state
        // In test environment, permissions are typically not granted
        XCTAssertFalse(
            actualPermissionState,
            "Should detect that permissions are actually revoked, ignoring stale cache")

        // And - The manager's state should be updated to reflect reality
        XCTAssertEqual(permissionManager.isAccessibilityPermissionGranted, actualPermissionState)

        // Cleanup
        permissionManager.clearPersistentState()
    }

    func testClearPersistentStateWorks() {
        // Given - Set some persistent state
        UserDefaults.standard.set(true, forKey: "hasEverBeenGranted")
        UserDefaults.standard.set(true, forKey: "lastKnownStatus")
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "lastCheckTimestamp")

        // When - Clear persistent state
        permissionManager.clearPersistentState()

        // Then - State should be cleared
        let state = permissionManager.getPersistentState()
        XCTAssertFalse(state.hasEverBeenGranted)
        XCTAssertFalse(state.lastKnownStatus)
        XCTAssertEqual(state.lastCheckTimestamp, 0)
    }
}
