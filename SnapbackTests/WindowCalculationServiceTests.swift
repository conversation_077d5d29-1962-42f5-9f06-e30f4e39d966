import XCTest

@testable import Snapback

final class WindowCalculationServiceTests: XCTestCase {
    var calculationService: WindowCalculationService!
    var mockScreen: MockNSScreen!

    override func setUp() {
        super.setUp()
        calculationService = WindowCalculationService()
        mockScreen = MockNSScreen()
    }

    override func tearDown() {
        calculationService = nil
        mockScreen = nil
        super.tearDown()
    }

    // MARK: - Helper Methods

    private func createWindowInfo(
        frame: CGRect = .zero,
        monitorID: UUID? = nil,
        appBundleIdentifier: String? = nil,
        isFullscreen: Bool = false
    ) -> WindowInfo {
        return WindowInfo(
            frame: frame,
            monitorID: monitorID,
            appBundleIdentifier: appBundleIdentifier,
            isFullscreen: isFullscreen
        )
    }

    // MARK: - Basic Position Tests

    func testCalculateLeftHalf() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .leftHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 0, y: 0, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateRightHalf() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .rightHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 1000, y: 0, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateTopHalf() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .topHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Top half should start at y = 500 (half height) in macOS coordinates
        let expected = CGRect(x: 0, y: 500, width: 2000, height: 500)
        XCTAssertEqual(result, expected)
    }

    func testCalculateBottomHalf() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .bottomHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Bottom half should start at y = 0 in macOS coordinates
        let expected = CGRect(x: 0, y: 0, width: 2000, height: 500)
        XCTAssertEqual(result, expected)
    }

    // MARK: - Third Position Tests

    func testCalculateLeftThird() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 3000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .leftThird,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 0, y: 0, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateCenterThird() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 3000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .centerThird,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 1000, y: 0, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateRightThird() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 3000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .rightThird,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 2000, y: 0, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    // MARK: - Quarter Position Tests

    func testCalculateTopLeftQuarter() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .topLeftQuarter,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Top quarter should start at y = 500 in macOS coordinates
        let expected = CGRect(x: 0, y: 500, width: 1000, height: 500)
        XCTAssertEqual(result, expected)
    }

    func testCalculateTopRightQuarter() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .topRightQuarter,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Top quarter should start at y = 500 in macOS coordinates
        let expected = CGRect(x: 1000, y: 500, width: 1000, height: 500)
        XCTAssertEqual(result, expected)
    }

    func testCalculateBottomLeftQuarter() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .bottomLeftQuarter,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Bottom quarter should start at y = 0 in macOS coordinates
        let expected = CGRect(x: 0, y: 0, width: 1000, height: 500)
        XCTAssertEqual(result, expected)
    }

    func testCalculateBottomRightQuarter() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .bottomRightQuarter,
            window: window,
            screen: mockScreen
        )

        // Then
        // Changed: Bottom quarter should start at y = 0 in macOS coordinates
        let expected = CGRect(x: 1000, y: 0, width: 1000, height: 500)
        XCTAssertEqual(result, expected)
    }

    // MARK: - Two-Thirds Position Tests

    func testCalculateLeftTwoThirds() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 3000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .leftTwoThirds,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateRightTwoThirds() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 3000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .rightTwoThirds,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 1000, y: 0, width: 2000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    // MARK: - Special Cases Tests

    func testCalculateMaximize() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .maximize,
            window: window,
            screen: mockScreen
        )

        // Then
        XCTAssertEqual(result, screenFrame)
    }

    func testCalculateCustomFrame() {
        // Given
        let window = createWindowInfo()
        let customFrame = CGRect(x: 100, y: 200, width: 300, height: 400)
        mockScreen.mockFrame = CGRect(x: 0, y: 0, width: 2000, height: 1000)

        // When
        let result = calculationService.calculateWindowRect(
            for: .custom(customFrame),
            window: window,
            screen: mockScreen
        )

        // Then
        XCTAssertEqual(result, customFrame)
    }

    // MARK: - Edge Cases and Validation

    func testCalculateWithNonZeroScreenOrigin() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 100, y: 200, width: 2000, height: 1000)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .leftHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        let expected = CGRect(x: 100, y: 200, width: 1000, height: 1000)
        XCTAssertEqual(result, expected)
    }

    func testCalculateWithOddScreenDimensions() {
        // Given
        let window = createWindowInfo()
        let screenFrame = CGRect(x: 0, y: 0, width: 1999, height: 999)
        mockScreen.mockFrame = screenFrame

        // When
        let result = calculationService.calculateWindowRect(
            for: .leftHalf,
            window: window,
            screen: mockScreen
        )

        // Then
        // Instead of checking for exact equality, check that the result is reasonable
        // The width should be approximately half of the screen width
        XCTAssertGreaterThanOrEqual(result.width, 999.0)
        XCTAssertLessThanOrEqual(result.width, 1000.0)

        // The height should match the screen height
        XCTAssertEqual(result.height, 999.0)

        // The origin should be at (0,0)
        XCTAssertEqual(result.origin.x, 0.0)
        XCTAssertEqual(result.origin.y, 0.0)
    }
}

// MARK: - Mock Classes

class MockNSScreen: NSScreen {
    var mockFrame: CGRect = .zero

    override var frame: CGRect {
        return mockFrame
    }

    override var visibleFrame: CGRect {
        return mockFrame
    }
}
