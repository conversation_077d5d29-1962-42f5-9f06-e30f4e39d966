import Carbon.HIToolbox
import KeyboardShortcuts
import XCTest

@testable import Snapback

final class ShortcutServiceTests: XCTestCase {
    var shortcutService: ShortcutService!
    var mockSnappingService: ShortcutMockWindowSnappingService!
    var mockAppDelegate: MockAppDelegate!
    var mockWorkspaceService: MockWorkspaceService!

    override func setUp() {
        super.setUp()
        mockSnappingService = ShortcutMockWindowSnappingService()
        mockAppDelegate = MockAppDelegate()
        mockWorkspaceService = MockWorkspaceService()

        shortcutService = ShortcutService(
            appDelegate: mockAppDelegate,
            snappingService: mockSnappingService,
            workspaceService: mockWorkspaceService
        )
    }

    override func tearDown() {
        shortcutService = nil
        mockSnappingService = nil
        mockAppDelegate = nil
        mockWorkspaceService = nil
        super.tearDown()
    }

    // MARK: - Test Methods

    func testRegisterKeyboardShortcuts() throws {
        // Given - Service is initialized
        XCTAssertNotNil(shortcutService, "ShortcutService should be initialized")

        // When - Register keyboard shortcuts (this should not throw)
        XCTAssertNoThrow(
            shortcutService.registerKeyboardShortcuts(),
            "registerKeyboardShortcuts should not throw")

        // Then - Service should still be functional
        XCTAssertNotNil(
            shortcutService, "ShortcutService should remain functional after registration")
    }

    func testUpdateShortcuts() throws {
        // Given - Service is initialized
        XCTAssertNotNil(shortcutService, "ShortcutService should be initialized")

        // When - Update shortcuts (this should not throw)
        XCTAssertNoThrow(shortcutService.updateShortcuts(), "updateShortcuts should not throw")

        // Then - Service should still be functional
        XCTAssertNotNil(shortcutService, "ShortcutService should remain functional after update")
    }

    func testShortcutRegistration() throws {
        // Given - Service is initialized and workspace service has workspaces
        let workspace = Workspace(
            id: UUID(),
            name: "Test Workspace",
            windowInfos: [],
            shortcutKeyCode: UInt16(kVK_ANSI_A),
            shortcutModifiers: NSEvent.ModifierFlags.command.rawValue
        )
        mockWorkspaceService.addWorkspace(workspace)

        // When - Register workspace shortcut immediately (this should not throw)
        XCTAssertNoThrow(
            shortcutService.registerWorkspaceShortcutImmediately(for: workspace),
            "registerWorkspaceShortcutImmediately should not throw"
        )

        // Then - Service should still be functional
        XCTAssertNotNil(
            shortcutService, "ShortcutService should remain functional after registration")
    }
}

// MARK: - Mock Classes

class ShortcutMockWindowSnappingService: WindowSnappingService {
    var snapCalled: Bool = false

    init() {
        super.init(
            windowMover: WindowMover(),
            calculationService: WindowCalculationService(),
            screenDetection: ScreenDetectionService()
        )
    }

    override func snapFrontmostWindow(to position: SnapPosition) {
        snapCalled = true
    }
}

class MockAppDelegate: AppDelegate {
    // Empty implementation for testing
    override init() {
        super.init()
    }
}

class MockWorkspaceService: WorkspaceService {
    init() {
        super.init(
            snappingService: WindowSnappingService(
                windowMover: WindowMover(),
                calculationService: WindowCalculationService(),
                screenDetection: ScreenDetectionService()
            ))
    }
}
